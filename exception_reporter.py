import threading
# import schedule  # 定时任务已取消，不再需要
# import time as time_module  # 定时任务已取消，不再需要

# 防重复发送的时间戳记录
_last_report_time = None
_report_lock = threading.Lock()
from datetime import datetime, timedelta, timezone
from time import time as now_time
from .kuaimai.kuaimai_api import KuaimaiAPI
try:
    from .bi.bi_api import BIAPI
except ImportError:
    from bi.bi_api import BIAPI
import yaml
import requests
import os
import logging
import json
from queue import Queue
try:
    import sys
    print(f"[DEBUG] Python路径: {sys.executable}")
    print(f"[DEBUG] 尝试导入pandas...")
    import pandas as pd
    from pathlib import Path
    EXCEL_AVAILABLE = True
    print(f"✅ pandas导入成功，版本: {pd.__version__}，Excel功能已启用")
except ImportError as e:
    EXCEL_AVAILABLE = False
    print(f"❌ 警告: pandas导入失败，Excel功能不可用。错误: {e}")
    print("请运行: pip install pandas openpyxl")
except Exception as e:
    EXCEL_AVAILABLE = False
    print(f"❌ 导入pandas时发生未知错误: {e}")

plugin_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(plugin_dir, 'config.yaml')

# 读取配置
try:
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 验证配置格式
    if not isinstance(config, dict):
        raise ValueError(f"配置文件格式错误，应该是字典格式，实际是: {type(config)}")

    kuaimai_config = config.get('kuaimai', {})
    bi_config = config.get('bi', {})
    dingtalk_config = config.get('dingtalk', {})

    # 验证各个配置项
    if not isinstance(kuaimai_config, dict):
        raise ValueError(f"kuaimai配置格式错误，应该是字典格式，实际是: {type(kuaimai_config)}")
    if not isinstance(bi_config, dict):
        raise ValueError(f"bi配置格式错误，应该是字典格式，实际是: {type(bi_config)}")
    if not isinstance(dingtalk_config, dict):
        raise ValueError(f"dingtalk配置格式错误，应该是字典格式，实际是: {type(dingtalk_config)}")

except Exception as e:
    print(f"❌ 配置文件读取失败: {e}")
    print(f"配置文件路径: {config_path}")
    # 使用默认配置
    kuaimai_config = {}
    bi_config = {}
    dingtalk_config = {}

# 初始化API（添加错误处理）
try:
    kuaimai_api = KuaimaiAPI(
        app_key=kuaimai_config.get('app_key', ''),
        secret=kuaimai_config.get('secret', ''),
        session=kuaimai_config.get('session', '')
    )
    print("✅ 快麦API初始化成功")
except Exception as e:
    print(f"❌ 快麦API初始化失败: {e}")
    kuaimai_api = None

try:
    token = bi_config.get('token', '')
    cookie = bi_config.get('cookie', '')

    if not token or not cookie:
        print(f"❌ BI API配置不完整: token={'已配置' if token else '未配置'}, cookie={'已配置' if cookie else '未配置'}")
        bi_api = None
    else:
        bi_api = BIAPI(
            token=token,
            cookie=cookie
        )
        print("✅ BI API初始化成功")
except Exception as e:
    print(f"❌ BI API初始化失败: {e}")
    bi_api = None

webhook_url = dingtalk_config.get('webhook_url')

# 独立日志logger，避免与主程序冲突
log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'exception_report_task.log')
logger = logging.getLogger("exception_report_task")
logger.setLevel(logging.INFO)
if not logger.handlers:
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    formatter = logging.Formatter('%(asctime)s %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

def get_status_text(status_code):
    status_map = {
        "WAIT_BUYER_PAY": "待付款",
        "WAIT_AUDIT": "待审核",
        "WAIT_FINANCE_AUDIT": "待财审",
        "FINISHED_AUDIT": "审核完成",
        "WAIT_EXPRESS_PRINT": "待打印快递单",
        "WAIT_PACKAGE": "待打包",
        "WAIT_WEIGHT": "待称重",
        "WAIT_SEND_GOODS": "待发货",
        "WAIT_DEST_SEND_GOODS": "待供销商发货",
        "SELLER_SEND_GOODS": "卖家已发货",
        "FINISHED": "交易完成",
        "CLOSED": "交易关闭",
    }
    return status_map.get(status_code, status_code or "未知状态")

def fetch_orders_for_day(start_time, end_time, page_size, logger, result_queue, tag):
    all_orders = []
    page_no = 1
    total_api_time = 0
    total_pages = 0
    while True:
        req_params = {
            "timeType": "pay_time",
            "startTime": start_time,
            "endTime": end_time,
            "status": "WAIT_AUDIT",
            "pageSize": page_size,
            "useHasNext": True
        }
        logger.info(f"[REPORT][{tag}] API请求参数: {req_params}")
        t0 = now_time()
        try:
            result = kuaimai_api._make_request(
                method="erp.trade.outstock.simple.query",
                business_params=req_params
            )
        except Exception as e:
            logger.error(f"[REPORT][{tag}] API请求异常: {str(e)}")
            for handler in logger.handlers:
                handler.flush()
            break
        logger.info(f"[REPORT][{tag}] API请求返回 : {json.dumps(result, ensure_ascii=False)[:2000]}")
        for handler in logger.handlers:
            handler.flush()
        t1 = now_time()
        api_cost = t1 - t0
        total_api_time += api_cost
        total_pages += 1
        logger.info(f"[REPORT][{tag}] 第{total_pages}页，耗时: {api_cost:.2f}s")
        order_list = result.get('list', [])
        all_orders.extend(order_list)
        logger.info(f"[REPORT][{tag}] 当前累计总订单数: {len(all_orders)}")
        for handler in logger.handlers:
            handler.flush()
        has_next = result.get('hasNext', False)
        if not has_next or not order_list:
            break
        page_no += 1
    logger.info(f"[REPORT][{tag}] 拉取完成，共{total_pages}页，总耗时{total_api_time:.2f}s，总订单数: {len(all_orders)}")
    for handler in logger.handlers:
        handler.flush()
    result_queue.put(all_orders)


def fetch_exception_orders():
    """
    获取异常订单数据（优化版本，避免重复拉取）
    """
    now = datetime.now()
    page_size = 200
    # 生成15个日期区间（今天~前14天）
    day_ranges = []
    for i in range(15):
        day = now - timedelta(days=i)
        start_time = day.strftime('%Y-%m-%d 00:00:00')
        end_time = day.strftime('%Y-%m-%d 23:59:59')
        tag = day.strftime('%Y-%m-%d')
        day_ranges.append((start_time, end_time, tag))

    # 多线程拉取
    threads = []
    result_queue = Queue()
    for start_time, end_time, tag in day_ranges:
        t = threading.Thread(target=fetch_orders_for_day, args=(start_time, end_time, page_size, logger, result_queue, tag))
        t.start()
        threads.append(t)
    for t in threads:
        t.join()

    # 合并所有订单
    all_orders = []
    while not result_queue.empty():
        all_orders.extend(result_queue.get())
    logger.info(f"[REPORT] 15天合计总订单数: {len(all_orders)}")

    # 筛选异常订单，跳过含EX_REFUND的订单
    exception_orders = [order for order in all_orders if order.get('exceptions') and (not (isinstance(order.get('exceptions'), list) and 'EX_REFUND' in order.get('exceptions')) and not (isinstance(order.get('exceptions'), str) and order.get('exceptions') == 'EX_REFUND'))]
    logger.info(f"[REPORT] 异常订单筛选完成，异常订单数: {len(exception_orders)}")

    return exception_orders

def generate_exception_report():
    """
    生成Markdown格式的异常订单报告
    """
    exception_orders = fetch_exception_orders()

    if not exception_orders:
        logger.info(f"[REPORT] 近15日暂无异常订单。")
        return f"# 异常订单报告\n\n近15日暂无异常订单。", exception_orders

    # 异常标签中英文映射
    exception_tag_map = {
        'EX_UNALLOCATED': '商品未匹配',
        'EX_INSUFFICIENT': '库存不足',
        'EX_RISK_ORDER': '风控订单',
        
        'EX_REFUND': '退款订单',
    }

    now = datetime.now()
    report = f"# 异常订单报告\n\n共 **{len(exception_orders)}** 个异常订单：\n"

    for idx, order in enumerate(exception_orders, 1):
        tid = order.get('tid', 'N/A')
        shop = order.get('shopName', order.get('shopId', 'N/A'))

        # 下单时间转为UTC+8日期时间
        created = order.get('created', '')
        if created:
            try:
                if isinstance(created, (int, float)):
                    dt = datetime.fromtimestamp(created/1000, tz=timezone.utc) + timedelta(hours=8)
                    created_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    created_str = str(created)
            except Exception:
                created_str = str(created)
        else:
            created_str = ''

        sys_status = order.get('sysStatus', 'N/A')
        status_text = get_status_text(sys_status)
        exceptions = order.get('exceptions', [])
        if isinstance(exceptions, str):
            exceptions = [exceptions]
        tag_str = '、'.join([exception_tag_map.get(tag, tag) for tag in exceptions]) if exceptions else '未知异常'

        report += f"\n## {idx}. 订单号：{tid}\n- **店铺**：{shop}\n- **下单时间**：{created_str}\n- **当前状态**：{status_text}\n- **异常标签**：{tag_str}\n"

        orders_items = order.get('orders', [])
        if orders_items:
            report += f"- **商品明细：**\n"
            for item in orders_items:
                goods_id = item.get('numIid') or item.get('oid') or item.get('id') or 'N/A'
                goods_name = item.get('title', 'N/A')
                manager_info = ''
                if goods_id != 'N/A' and bi_api is not None:
                    try:
                        bi_result = bi_api.query_goods_manager([str(goods_id)])
                        if bi_result and bi_result.get('success', False):
                            data = bi_result.get('data')
                            manager_list = []
                            if data is None:
                                # data为None代表BI中暂无该商品负责人
                                manager_info = 'BI中暂无该商品负责人信息'
                            elif isinstance(data, dict):
                                for goods in data.get('list', []):
                                    managers = goods.get('managerList', [])
                                    if managers:
                                        manager_names = [m.get('userName', 'N/A') for m in managers]
                                        manager_list.extend(manager_names)
                                if manager_list:
                                    manager_info = '、'.join(manager_list)
                                else:
                                    manager_info = 'BI中暂无该商品负责人信息'
                            else:
                                manager_info = 'BI中暂无该商品负责人信息'
                        else:
                            manager_info = 'BI中暂无该商品负责人信息'
                    except Exception as e:
                        print(f"查询负责人信息失败: {e}")
                        manager_info = 'BI查询失败'
                else:
                    manager_info = 'BI中暂无该商品负责人信息'
                sys_sku_properties = item.get('sysSkuPropertiesName', 'N/A')
                report += f"    - 商品ID：{goods_id}  名称：{goods_name}  负责人：{manager_info}  规格：{sys_sku_properties}\n"

    report += f"\n---\n生成时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"
    logger.info(f"[REPORT] 报告生成完成，总异常订单: {len(exception_orders)}")

    return report, exception_orders

def generate_excel_report(exception_orders):
    """
    生成异常订单Excel报告（优化版本）
    """
    if not EXCEL_AVAILABLE:
        print("pandas未安装，无法生成Excel报告")
        return None

    if not exception_orders:
        print("无异常订单，跳过Excel生成")
        return None

    try:
        # 异常标签映射
        exception_tag_map = {
            'EX_UNALLOCATED': '商品未匹配',
            'EX_INSUFFICIENT': '库存不足',
            'EX_RISK_ORDER': '风控订单',
            'EX_REFUND': '退款订单',
        }

        # 批量获取所有商品ID的负责人信息（优化BI查询）
        all_goods_ids = set()
        for order in exception_orders:
            orders_items = order.get('orders', [])
            for item in orders_items:
                goods_id = item.get('numIid') or item.get('oid') or item.get('id')
                if goods_id and goods_id != 'N/A':
                    all_goods_ids.add(str(goods_id))

        # 批量查询负责人信息
        manager_cache = {}
        if all_goods_ids and bi_api is not None:
            try:
                bi_result = bi_api.query_goods_manager(list(all_goods_ids))
                if bi_result and bi_result.get('success', False):
                    data = bi_result.get('data')
                    if data is None:
                        # data为None代表BI中暂无这些商品的负责人信息
                        logger.info("[REPORT] BI中暂无这些商品的负责人信息")
                    elif isinstance(data, dict):
                        for goods in data.get('list', []):
                            goods_id = str(goods.get('goodsId', ''))
                            managers = goods.get('managerList', [])
                            if managers:
                                manager_names = [m.get('userName', 'N/A') for m in managers]
                                manager_cache[goods_id] = '、'.join(manager_names)
            except Exception as e:
                logger.warning(f"[REPORT] 批量查询负责人信息失败: {e}")
        elif bi_api is None:
            print("⚠️ BI API未初始化，无法查询负责人信息")

        # 快速生成Excel数据
        excel_data = []
        for idx, order in enumerate(exception_orders, 1):
            tid = order.get('tid', 'N/A')
            shop = order.get('shopName', order.get('shopId', 'N/A'))

            # 处理下单时间
            created = order.get('created', '')
            if created:
                try:
                    if isinstance(created, (int, float)):
                        dt = datetime.fromtimestamp(created/1000, tz=timezone.utc) + timedelta(hours=8)
                        created_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        created_str = str(created)
                except Exception:
                    created_str = str(created)
            else:
                created_str = ''

            sys_status = order.get('sysStatus', 'N/A')
            status_text = get_status_text(sys_status)

            # 处理异常标签
            exceptions = order.get('exceptions', [])
            if isinstance(exceptions, str):
                exceptions = [exceptions]
            tag_str = '、'.join([exception_tag_map.get(tag, tag) for tag in exceptions]) if exceptions else '未知异常'

            # 处理商品信息
            orders_items = order.get('orders', [])
            if orders_items:
                for item in orders_items:
                    goods_id = item.get('numIid') or item.get('oid') or item.get('id') or 'N/A'
                    goods_name = item.get('title', 'N/A')

                    # 从缓存中获取负责人信息
                    manager_info = manager_cache.get(str(goods_id), 'BI中暂无该商品负责人信息') if goods_id != 'N/A' else 'BI中暂无该商品负责人信息'

                    excel_data.append({
                        '序号': idx,
                        '订单号': tid,
                        '店铺': shop,
                        '下单时间': created_str,
                        '当前状态': status_text,
                        '异常标签': tag_str,
                        '商品ID': goods_id,
                        '商品名称': goods_name,
                        '负责人': manager_info,
                        '规格': item.get('sysSkuPropertiesName', 'N/A')
                    })
            else:
                # 没有商品信息的情况
                excel_data.append({
                    '序号': idx,
                    '订单号': tid,
                    '店铺': shop,
                    '下单时间': created_str,
                    '当前状态': status_text,
                    '异常标签': tag_str,
                    '商品ID': 'N/A',
                    '商品名称': 'N/A',
                    '负责人': 'N/A',
                    '规格': 'N/A'
                })

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 生成文件名
        now = datetime.now()
        filename = f"异常订单报告_{now.strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path = Path(plugin_dir) / filename

        # 美化Excel样式
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        from openpyxl.utils.dataframe import dataframe_to_rows

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "异常订单报告"

        # 添加标题行
        title_row = ["序号", "订单号", "店铺", "下单时间", "当前状态", "异常标签", "商品ID", "商品名称", "负责人", "规格"]
        ws.append(title_row)

        # 添加数据行
        for row in dataframe_to_rows(df, index=False, header=False):
            ws.append(row)

        # 样式定义
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        data_font = Font(name='微软雅黑', size=10)
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        center_alignment = Alignment(horizontal='center', vertical='center')
        left_alignment = Alignment(horizontal='left', vertical='center')

        # 设置标题行样式
        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = center_alignment

        # 设置数据行样式
        for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
            for i, cell in enumerate(row):
                cell.font = data_font
                cell.border = border
                # 序号、商品ID居中对齐，其他左对齐
                if i in [0, 6]:  # 序号和商品ID列
                    cell.alignment = center_alignment
                else:
                    cell.alignment = left_alignment

        # 设置列宽
        column_widths = {
            'A': 8,   # 序号
            'B': 20,  # 订单号
            'C': 15,  # 店铺
            'D': 20,  # 下单时间
            'E': 12,  # 当前状态
            'F': 15,  # 异常标签
            'G': 15,  # 商品ID
            'H': 30,  # 商品名称
            'I': 15,  # 负责人
            'J': 20   # 规格
        }

        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width

        # 设置行高
        for row in ws.iter_rows():
            ws.row_dimensions[row[0].row].height = 25

        # 冻结首行
        ws.freeze_panes = 'A2'

        # 添加筛选器
        ws.auto_filter.ref = f"A1:J{ws.max_row}"

        # 保存文件
        wb.save(file_path)

        logger.info(f"[REPORT] 美化Excel报告已生成: {file_path}")
        print(f"美化Excel报告已生成: {file_path}")

        return str(file_path)

    except Exception as e:
        logger.error(f"[REPORT] 生成Excel报告失败: {str(e)}")
        print(f"生成Excel报告失败: {str(e)}")
        return None

def send_to_dingtalk(report_md):
    if not webhook_url:
        print("未配置钉钉webhook_url，无法发送报告")
        return
    data = {
        "msgtype": "markdown",
        "markdown": {
            "title": "异常订单报告",
            "text": report_md + "\n\n"
        },
        "at": {
            "isAtAll": True
        }
    }
    try:
        resp = requests.post(webhook_url, json=data, timeout=10)
        if resp.status_code == 200:
            print("报告已发送到钉钉群")
        else:
            print(f"发送钉钉失败: {resp.text}")
    except Exception as e:
        print(f"发送钉钉异常: {e}")

def send_excel_to_dingtalk(excel_file_path, exception_count=0):
    """
    发送Excel文件到钉钉群，发送完成后自动删除文件，并发送@全体提醒
    """
    try:
        # 导入钉钉SDK（使用与main.py相同的导入模式）
        try:
            from .dingtalk.dingtalk_sdk import DingTalkSDK
            from .dingtalk.config import DingTalkConfig
            print("✅ 钉钉SDK相对导入成功")
        except ImportError as e:
            print(f"⚠️ 相对导入失败: {e}")
            try:
                # 兼容独立运行
                from dingtalk.dingtalk_sdk import DingTalkSDK
                from dingtalk.config import DingTalkConfig
                print("✅ 钉钉SDK绝对导入成功")
            except ImportError as e2:
                print(f"❌ 绝对导入也失败: {e2}")
                raise ImportError(f"无法导入钉钉SDK: 相对导入({e}), 绝对导入({e2})")

        # 加载配置
        config = DingTalkConfig()
        print(f"📋 钉钉配置加载完成: {config}")

        # 检查必要配置
        if not config.is_valid():
            missing_fields = config.get_missing_configs()
            print(f"钉钉配置不完整，缺少字段: {', '.join(missing_fields)}")
            logger.error(f"[REPORT] 钉钉配置不完整，缺少字段: {', '.join(missing_fields)}")
            # 删除文件
            try:
                if os.path.exists(excel_file_path):
                    os.remove(excel_file_path)
                    print(f"已删除Excel文件: {excel_file_path}")
            except Exception as e:
                print(f"删除Excel文件失败: {e}")
            return False

        # 初始化SDK
        sdk = DingTalkSDK(
            app_key=config.app_key,
            app_secret=config.app_secret
        )

        print(f"📤 开始使用新SDK发送Excel文件: {os.path.basename(excel_file_path)}")
        logger.info(f"[REPORT] 开始使用新SDK发送Excel文件: {excel_file_path}")

        # 上传并发送文件到群会话
        result = sdk.upload_file_to_group_conversation(
            file_path=excel_file_path,
            file_name=os.path.basename(excel_file_path),
            open_conversation_id=config.open_conversation_id,
            space_id=config.space_id,
            user_id=config.user_id
        )

        print("✅ Excel文件已发送到钉钉群")
        logger.info(f"[REPORT] Excel文件已发送到钉钉群: {result.name}")

        # 发送@全体成员的提醒消息
        try:
            if exception_count > 0:
                content = f"📋 有{exception_count} 笔新的异常订单，请及时处理！\n📊 详细信息请查看上方Excel文件"
            else:
                content = "📋 异常订单报告已更新，请查看上方Excel文件"

            reminder_data = {
                "msgtype": "text",
                "text": {
                    "content": content
                },
                "at": {
                    "isAtAll": True
                }
            }

            reminder_resp = requests.post(webhook_url, json=reminder_data, timeout=10)
            if reminder_resp.status_code == 200:
                print("✅ @全体成员提醒消息已发送")
                logger.info("[REPORT] @全体成员提醒消息已发送")
            else:
                print(f"⚠️ 提醒消息发送失败: {reminder_resp.text}")
                logger.warning(f"[REPORT] 提醒消息发送失败: {reminder_resp.text}")
        except Exception as e:
            print(f"⚠️ 发送提醒消息异常: {e}")
            logger.warning(f"[REPORT] 发送提醒消息异常: {e}")

        # 发送成功后删除文件
        try:
            if os.path.exists(excel_file_path):
                os.remove(excel_file_path)
                print(f"✅ 已自动删除Excel文件: {excel_file_path}")
                logger.info(f"[REPORT] 已自动删除Excel文件: {excel_file_path}")
        except Exception as e:
            print(f"删除Excel文件失败: {e}")
            logger.error(f"[REPORT] 删除Excel文件失败: {e}")

        return True

    except ImportError:
        print("无法导入钉钉发送模块")
        logger.error("[REPORT] 无法导入钉钉发送模块")
        # 删除文件
        try:
            if os.path.exists(excel_file_path):
                os.remove(excel_file_path)
                print(f"已删除Excel文件: {excel_file_path}")
        except Exception as e:
            print(f"删除Excel文件失败: {e}")
        return False
    except Exception as e:
        print(f"发送Excel文件到钉钉失败: {str(e)}")
        logger.error(f"[REPORT] 发送Excel文件到钉钉失败: {str(e)}")
        # 删除文件
        try:
            if os.path.exists(excel_file_path):
                os.remove(excel_file_path)
                print(f"已删除Excel文件: {excel_file_path}")
        except Exception as e:
            print(f"删除Excel文件失败: {e}")
        return False

# 创建一个包装函数，用于在main.py中被开关控制
def send_excel_to_dingtalk_with_switch(excel_file_path, exception_count=0):
    """
    带开关控制的Excel发送函数，会被main.py重写
    """
    return send_excel_to_dingtalk(excel_file_path, exception_count)

# 已删除重复的 exception_report_job() 函数，统一使用 exception_report_job_optimized()

def exception_report_job():
    """
    异常订单报告任务，生成并发送Excel文件到钉钉群
    """
    try:
        # 获取异常订单数据
        exception_orders = fetch_exception_orders()

        # 生成Markdown报告（使用传入的数据）
        if not exception_orders:
            report = "# 异常订单报告\n\n近15日暂无异常订单。"
        else:
            # 重用generate_exception_report的逻辑，但传入数据
            exception_tag_map = {
                'EX_UNALLOCATED': '商品未匹配',
                'EX_INSUFFICIENT': '库存不足',
                'EX_RISK_ORDER': '风控订单',
                'EX_REFUND': '退款订单',
            }

            now = datetime.now()
            report = f"# 异常订单报告\n\n共 **{len(exception_orders)}** 个异常订单：\n"

            for idx, order in enumerate(exception_orders, 1):
                tid = order.get('tid', 'N/A')
                shop = order.get('shopName', order.get('shopId', 'N/A'))

                # 下单时间转为UTC+8日期时间
                created = order.get('created', '')
                if created:
                    try:
                        if isinstance(created, (int, float)):
                            dt = datetime.fromtimestamp(created/1000, tz=timezone.utc) + timedelta(hours=8)
                            created_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            created_str = str(created)
                    except Exception:
                        created_str = str(created)
                else:
                    created_str = ''

                sys_status = order.get('sysStatus', 'N/A')
                status_text = get_status_text(sys_status)
                exceptions = order.get('exceptions', [])
                if isinstance(exceptions, str):
                    exceptions = [exceptions]
                tag_str = '、'.join([exception_tag_map.get(tag, tag) for tag in exceptions]) if exceptions else '未知异常'

                report += f"\n## {idx}. 订单号：{tid}\n- **店铺**：{shop}\n- **下单时间**：{created_str}\n- **当前状态**：{status_text}\n- **异常标签**：{tag_str}\n"

                orders_items = order.get('orders', [])
                if orders_items:
                    report += f"- **商品明细：**\n"
                    for item in orders_items:
                        goods_id = item.get('numIid') or item.get('oid') or item.get('id') or 'N/A'
                        goods_name = item.get('title', 'N/A')
                        manager_info = ''
                        if goods_id != 'N/A' and bi_api is not None:
                            try:
                                bi_result = bi_api.query_goods_manager([str(goods_id)])
                                if bi_result and bi_result.get('success', False):
                                    data = bi_result.get('data')
                                    manager_list = []
                                    if data is None:
                                        # data为None代表BI中暂无该商品负责人
                                        manager_info = 'BI中暂无该商品负责人信息'
                                    elif isinstance(data, dict):
                                        for goods in data.get('list', []):
                                            managers = goods.get('managerList', [])
                                            if managers:
                                                manager_names = [m.get('userName', 'N/A') for m in managers]
                                                manager_list.extend(manager_names)
                                        if manager_list:
                                            manager_info = '、'.join(manager_list)
                                        else:
                                            manager_info = 'BI中暂无该商品负责人信息'
                                    else:
                                        manager_info = 'BI中暂无该商品负责人信息'
                                else:
                                    manager_info = 'BI中暂无该商品负责人信息'
                            except Exception as e:
                                print(f"查询负责人信息失败: {e}")
                                manager_info = 'BI查询失败'
                        else:
                            manager_info = 'BI中暂无该商品负责人信息'
                        sys_sku_properties = item.get('sysSkuPropertiesName', 'N/A')
                        report += f"    - 商品ID：{goods_id}  名称：{goods_name}  负责人：{manager_info}  规格：{sys_sku_properties}\n"

            report += f"\n---\n生成时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"

        print(report)
        logger.info("\n" + report)

        # 发送Markdown报告（已注释，只发送Excel文件）
        # send_to_dingtalk(report)

        # 生成并发送Excel报告
        if exception_orders:
            excel_file_path = generate_excel_report(exception_orders)
            if excel_file_path:
                send_excel_to_dingtalk_with_switch(excel_file_path, len(exception_orders))

        return report, exception_orders

    except Exception as e:
        print(f"异常订单报告任务失败: {str(e)}")
        logger.error(f"[REPORT] 异常订单报告任务失败: {str(e)}")
        return None, []

def scheduled_report_job():
    """
    定时任务包装函数，带防重复机制
    """
    global _last_report_time

    try:
        current_time = datetime.now()
        current_hour = current_time.hour
        current_date = current_time.date()

        # 检查是否在允许的时间段内（9点或16点）
        if current_hour not in [9, 16]:
            print(f"⚠️ 当前时间 {current_time.strftime('%H:%M')} 不在允许的报告时间内（9:00或16:00）")
            return

        with _report_lock:
            # 检查是否已经在当前小时内发送过报告
            if _last_report_time is not None:
                last_date = _last_report_time.date()
                last_hour = _last_report_time.hour

                if last_date == current_date and last_hour == current_hour:
                    print(f"⚠️ 今日 {current_hour}:00 时段已发送过异常订单报告，跳过重复发送")
                    logger.info(f"[REPORT] 今日 {current_hour}:00 时段已发送过异常订单报告，跳过重复发送")
                    return

            print(f"🔄 开始执行 {current_time.strftime('%Y-%m-%d %H:%M')} 异常订单报告任务")
            logger.info(f"[REPORT] 开始执行 {current_time.strftime('%Y-%m-%d %H:%M')} 异常订单报告任务")

            # 执行报告任务
            exception_report_job()

            # 更新最后发送时间
            _last_report_time = current_time
            print(f"✅ {current_time.strftime('%Y-%m-%d %H:%M')} 异常订单报告任务完成")
            logger.info(f"[REPORT] {current_time.strftime('%Y-%m-%d %H:%M')} 异常订单报告任务完成")

    except Exception as e:
        print(f"定时异常订单报告失败: {e}")
        logger.error(f"[REPORT] 定时异常订单报告失败: {e}")

def reset_report_time():
    """重置报告时间，用于手动测试"""
    global _last_report_time
    with _report_lock:
        _last_report_time = None
        print("✅ 异常订单报告时间已重置")
        logger.info("[REPORT] 异常订单报告时间已重置")

# 定时任务已取消，改为手动触发
# class ExceptionReportThread(threading.Thread):
#     def run(self):
#         # 定时任务：每天9点和16点推送异常订单报告
#         schedule.every().day.at("09:00").do(scheduled_report_job)
#         schedule.every().day.at("16:00").do(scheduled_report_job)
#         print("✅ 异常订单定时任务已启动：每天9:00和16:00执行")
#         logger.info("[REPORT] 异常订单定时任务已启动：每天9:00和16:00执行")
#         while True:
#             schedule.run_pending()
#             time_module.sleep(30)

# exception_thread = ExceptionReportThread()
# exception_thread.daemon = True
# exception_thread.start()

print("📝 异常订单定时任务已取消，改为手动触发模式")
logger.info("[REPORT] 异常订单定时任务已取消，改为手动触发模式")