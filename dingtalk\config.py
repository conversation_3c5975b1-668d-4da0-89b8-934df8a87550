#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
钉钉SDK配置文件
"""

import os
import yaml
from typing import Optional


class DingTalkConfig:
    """钉钉配置类"""

    def __init__(self, config_path: Optional[str] = None):
        """
        从配置文件或环境变量加载配置

        Args:
            config_path: 配置文件路径，如果不提供则使用默认路径
        """
        # 默认配置
        self.app_key = ""
        self.app_secret = ""
        self.access_token = ""  # 将通过app_key和app_secret自动获取
        self.webhook_url = ""
        self.space_id = ""
        self.union_id = ""  # 将通过userid自动获取
        self.open_conversation_id = ""
        self.user_id = ""
        self.robot_code = ""
        self.default_parent_id = "0"  # 根目录
        self.target_folder_id = "183652899403"  # 目标文件夹ID（仅作为参考）
        self.base_url = 'https://api.dingtalk.com'

        # 从配置文件加载
        if config_path is None:
            # 获取当前文件所在目录的上级目录中的config.yaml
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(os.path.dirname(current_dir), 'config.yaml')

        self._load_from_yaml(config_path)

        # 从环境变量加载（环境变量优先级高于配置文件）
        self._load_from_env()

        # 如果关键配置仍然为空，使用默认值
        if not self.app_key:
            self.app_key = "ding6gwkdk24m4hypb2r"
        if not self.app_secret:
            self.app_secret = "TU7W4hKsbDoHNGmVqEZBx2FOlmE5DPKvFQZb-eRUiK094_32PIevtNe_PAgLEHJB"
        if not self.open_conversation_id:
            self.open_conversation_id = "cidhGFS6BNJ7MS4NH+ULZy9bA=="
        if not self.space_id:
            self.space_id = "26872825752"
        if not self.user_id:
            self.user_id = "02331805405724463952"
        if not self.robot_code:
            self.robot_code = "ding6gwkdk24m4hypb2r"

    def _load_from_yaml(self, config_path: str):
        """从YAML配置文件加载配置"""
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)

                if config_data and 'dingtalk' in config_data:
                    dingtalk_config = config_data['dingtalk']
                    self.app_key = dingtalk_config.get('app_key', self.app_key)
                    self.app_secret = dingtalk_config.get('app_secret', self.app_secret)
                    self.webhook_url = dingtalk_config.get('webhook_url', self.webhook_url)
                    self.robot_code = dingtalk_config.get('robot_code', self.robot_code)
                    self.open_conversation_id = dingtalk_config.get('open_conversation_id', self.open_conversation_id)
                    self.space_id = dingtalk_config.get('space_id', self.space_id)
                    self.user_id = dingtalk_config.get('user_id', self.user_id)
                    self.default_parent_id = dingtalk_config.get('default_parent_id', self.default_parent_id)
        except Exception as e:
            print(f"加载YAML配置文件失败: {e}")

    def _load_from_env(self):
        """从环境变量加载配置"""
        self.app_key = os.getenv('DINGTALK_APP_KEY', self.app_key)
        self.app_secret = os.getenv('DINGTALK_APP_SECRET', self.app_secret)
        self.access_token = os.getenv('DINGTALK_ACCESS_TOKEN', self.access_token)
        self.webhook_url = os.getenv('DINGTALK_WEBHOOK_URL', self.webhook_url)
        self.space_id = os.getenv('DINGTALK_SPACE_ID', self.space_id)
        self.union_id = os.getenv('DINGTALK_UNION_ID', self.union_id)
        self.open_conversation_id = os.getenv('DINGTALK_OPEN_CONVERSATION_ID', self.open_conversation_id)
        self.user_id = os.getenv('DINGTALK_USER_ID', self.user_id)
        self.robot_code = os.getenv('DINGTALK_ROBOT_CODE', self.robot_code)
        self.base_url = os.getenv('DINGTALK_BASE_URL', self.base_url)
    
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        # 现在使用固定的app_key和app_secret，所以总是有效的
        return True
    
    def get_missing_configs(self) -> list:
        """获取缺失的配置项"""
        # 现在使用固定配置，没有缺失项
        return []
    
    def __str__(self):
        """配置信息字符串表示"""
        return f"""
钉钉SDK配置:
- App Key: {self.app_key}
- App Secret: {'已配置' if self.app_secret else '未配置'}
- Access Token: {'将自动获取' if not self.access_token else '已配置'}
- Space ID: {self.space_id}
- User ID: {self.user_id}
- Union ID: {'将自动获取' if not self.union_id else self.union_id}
- Open Conversation ID: {self.open_conversation_id}
- Robot Code: {self.robot_code}
- Default Parent ID: {self.default_parent_id}
- Target Folder ID: {self.target_folder_id}
- Base URL: {self.base_url}
"""


# 全局配置实例
config = DingTalkConfig()


def load_config_from_file(config_file: str = 'dingtalk_config.json') -> Optional[DingTalkConfig]:
    """从JSON文件加载配置"""
    import json
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        cfg = DingTalkConfig()
        cfg.access_token = data.get('access_token', cfg.access_token)
        cfg.space_id = data.get('space_id', cfg.space_id)
        cfg.union_id = data.get('union_id', cfg.union_id)
        cfg.base_url = data.get('base_url', cfg.base_url)
        
        return cfg
    except FileNotFoundError:
        print(f"配置文件 {config_file} 不存在")
        return None
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return None


def save_config_to_file(cfg: DingTalkConfig, config_file: str = 'dingtalk_config.json'):
    """保存配置到JSON文件"""
    import json
    
    data = {
        'access_token': cfg.access_token,
        'space_id': cfg.space_id,
        'union_id': cfg.union_id,
        'base_url': cfg.base_url
    }
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"配置已保存到 {config_file}")


def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "access_token": "your_access_token_here",
        "space_id": "your_space_id_here", 
        "union_id": "your_union_id_here",
        "base_url": "https://api.dingtalk.com"
    }
    
    import json
    with open('dingtalk_config_sample.json', 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)
    
    print("示例配置文件 dingtalk_config_sample.json 已创建")
    print("请复制为 dingtalk_config.json 并填入正确的配置信息")


if __name__ == "__main__":
    print("钉钉SDK配置管理")
    print("=" * 30)
    
    # 显示当前配置
    print(config)
    
    # 检查配置有效性
    if config.is_valid():
        print("✅ 配置有效")
    else:
        missing = config.get_missing_configs()
        print(f"❌ 配置无效，缺失: {', '.join(missing)}")
        
        # 创建示例配置文件
        create_sample_config()
        
        print("\n配置方式:")
        print("1. 修改 config.py 文件中的默认值")
        print("2. 设置环境变量:")
        print("   export DINGTALK_ACCESS_TOKEN=your_token")
        print("   export DINGTALK_SPACE_ID=your_space_id")
        print("   export DINGTALK_UNION_ID=your_union_id")
        print("3. 使用配置文件 dingtalk_config.json")
