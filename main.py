import sys
sys.dont_write_bytecode = True
import os
import yaml

plugin_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(plugin_dir, 'config.yaml')
with open(config_path, 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# AstrBot 框架相关import
try:
    from astrbot.api.event import filter, AstrMessageEvent, MessageEventResult
    from astrbot.api.star import Context, Star, register
    from astrbot.api import logger
except ImportError:
    # 兼容独立运行
    pass

# 你的自定义API
try:
    from .kuaimai.kuaimai_api import KuaimaiAPI
    from .dingtalk.dingtalk_api import DingTalkAPI
    from .bi.bi_api import BIAPI
    from . import exception_reporter
except ImportError:
    # 兼容独立运行
    from kuaimai.kuaimai_api import KuaimaiAPI
    from dingtalk.dingtalk_api import DingTalkAPI
    from bi.bi_api import BIAPI
    import exception_reporter

kuaimai_config = config['kuaimai']
dingtalk_config = config['dingtalk']
bi_config = config['bi']

kuaimai_log_level = config.get('log_level', 'INFO') if isinstance(config, dict) else 'INFO'

kuaimai_api = KuaimaiAPI(
    app_key=kuaimai_config['app_key'],
    secret=kuaimai_config['secret'],
    session=kuaimai_config['session'],
    log_level=kuaimai_log_level
)
dingtalk_api = DingTalkAPI(
    app_key=dingtalk_config['app_key'],
    app_secret=dingtalk_config['app_secret'],
    webhook_url=dingtalk_config['webhook_url'],
    log_level=kuaimai_log_level
)
# 初始化BI API（添加错误处理）
try:
    token = bi_config.get('token', '')
    cookie = bi_config.get('cookie', '')

    if not token or not cookie:
        print(f"❌ [main.py] BI API配置不完整: token={'已配置' if token else '未配置'}, cookie={'已配置' if cookie else '未配置'}")
        bi_api = None
    else:
        bi_api = BIAPI(
            token=token,
            cookie=cookie,
            log_level=kuaimai_log_level
        )
        print("✅ [main.py] BI API初始化成功")
except Exception as e:
    print(f"❌ [main.py] BI API初始化失败: {e}")
    bi_api = None


@register("xiyabot", "Soulter", "汐娅专属的Bot - 快麦API订单查询插件", "1.3.0")
class XiyaBot(Star):
    def __init__(self, context: Context):
        super().__init__(context)
        # 快麦API配置 - 请替换为您的实际配置
        self.kuaimai_api = kuaimai_api
        self.dingtalk_api = dingtalk_api
        self.bi_api = bi_api
        
    async def initialize(self):
        """初始化快麦API客户端和钉钉API客户端"""
        try:
            # 检查快麦API
            if self.kuaimai_api:
                logger.info("快麦API客户端初始化成功")
            else:
                logger.warning("快麦API未初始化")

            # 检查钉钉API
            if self.dingtalk_api:
                logger.info("钉钉API客户端初始化成功")
            else:
                logger.warning("钉钉API配置不完整，钉钉功能将不可用")

            # 检查BI API
            if self.bi_api:
                logger.info("BI API客户端初始化成功")
            else:
                logger.warning("BI API未初始化，负责人查询功能将不可用")

        except Exception as e:
            logger.error(f"API客户端初始化失败: {e}")

    def _get_status_text(self, status_code):
        status_map = {
            "WAIT_BUYER_PAY": "待付款",
            "WAIT_AUDIT": "待审核",
            "WAIT_FINANCE_AUDIT": "待财审",
            "FINISHED_AUDIT": "审核完成",
            "WAIT_EXPRESS_PRINT": "待打印快递单",
            "WAIT_PACKAGE": "待打包",
            "WAIT_WEIGHT": "待称重",
            "WAIT_SEND_GOODS": "待发货",
            "WAIT_DEST_SEND_GOODS": "待供销商发货",
            "SELLER_SEND_GOODS": "卖家已发货",
            "FINISHED": "交易完成",
            "CLOSED": "交易关闭",
        }
        return status_map.get(status_code, status_code or "未知状态")

    @filter.command("查询订单")
    async def query_order(self, event: AstrMessageEvent):
        """
        查询订单信息 - 显示所有可获取参数（新版/美化）
        """
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        message_str = event.message_str.strip()
        order_number = message_str.split()[-1] if message_str.split() else ""

        if not order_number:
            yield event.plain_result("请提供订单号\n使用方法: `/查询订单 订单号`")
            return

        # 通过API直接查订单号
        result = self.kuaimai_api._make_request(
            method="erp.trade.outstock.simple.query",
            business_params={"tid": order_number}
        )
        order_list = result.get("list", [])
        if not order_list:
            yield event.plain_result("未找到该订单信息")
            return
        order_info = order_list[0]
        # 美化输出所有参数
        markdown = self.kuaimai_api.format_full_order_markdown(order_info)
        yield event.plain_result(markdown)

    @filter.command("id查询")
    async def query_by_id(self, event: AstrMessageEvent):
        """通过ID查询订单 - 支持多种ID类型，包含商品ID、SKUID、所属店铺"""
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        message_str = event.message_str.strip()
        params = message_str.split()

        if len(params) < 2:
            yield event.plain_result(
                "请提供ID信息\n\n"
                "使用方法:\n"
                "• /id查询 订单号 - 查询单个订单\n"
                "• /id查询 时间 2024-06-14 - 查询指定日期订单\n"
                "• /id查询 今天 - 查询今天订单\n"
                "• /id查询 昨天 - 查询昨天订单"
            )
            return

        query_param = params[1]

        try:
            # 特殊时间查询
            if query_param in ["今天", "今日"]:
                from datetime import datetime
                today = datetime.now()
                start_time = today.strftime("%Y-%m-%d 00:00:00")
                end_time = today.strftime("%Y-%m-%d 23:59:59")

                result = self.kuaimai_api.query_orders_by_time(start_time, end_time, "pay_time")

                if not result.get("success", False):
                    yield event.plain_result(f"查询失败: {result.get('msg', '未知错误')}")
                    return

                order_list = result.get("list", [])
                if not order_list:
                    yield event.plain_result("今天暂无订单")
                    return

                summary = f"# 📋 今日订单汇总\n\n"
                summary += f"📊 **订单总数**: {len(order_list)} 个\n\n"

                # 统计各状态订单数量
                status_count = {}
                total_amount = 0
                for order in order_list:
                    status = order.get('sysStatus', 'N/A')
                    status_text = self._get_status_text(status)
                    status_count[status_text] = status_count.get(status_text, 0) + 1
                    try:
                        total_amount += float(order.get('payment', 0))
                    except:
                        pass

                summary += "## 📈 状态统计\n"
                for status, count in status_count.items():
                    summary += f"- **{status}**: {count} 个\n"

                summary += f"\n💰 **总金额**: ¥{total_amount:.2f}\n\n"

                summary += "## 📝 订单列表\n"
                for i, order in enumerate(order_list[:10], 1):
                    tid = order.get('tid', 'N/A')
                    shop = order.get('shopName', order.get('shopId', 'N/A'))
                    sys_status = order.get('sysStatus', 'N/A')
                    status_text = self._get_status_text(sys_status)
                    payment = order.get('payment', '0')

                    summary += f"**{i}.** `{tid}`\n"
                    summary += f"   🏪 {shop} | 📊 {status_text} | 💰 ¥{payment}\n\n"

                if len(order_list) > 10:
                    summary += f"📄 *还有 {len(order_list) - 10} 个订单未显示*\n\n"

                summary += "💡 使用 `查询订单 订单号` 查看详细信息"
                yield event.plain_result(summary)

            elif query_param in ["昨天", "昨日"]:
                from datetime import datetime, timedelta
                yesterday = datetime.now() - timedelta(days=1)
                start_time = yesterday.strftime("%Y-%m-%d 00:00:00")
                end_time = yesterday.strftime("%Y-%m-%d 23:59:59")

                result = self.kuaimai_api.query_orders_by_time(start_time, end_time, "pay_time")

                if not result.get("success", False):
                    yield event.plain_result(f"查询失败: {result.get('msg', '未知错误')}")
                    return

                order_list = result.get("list", [])
                if not order_list:
                    yield event.plain_result("昨天暂无订单")
                    return

                summary = f"# 📋 昨日订单汇总\n\n"
                summary += f"📊 **订单总数**: {len(order_list)} 个\n\n"

                # 统计各状态订单数量
                status_count = {}
                total_amount = 0
                for order in order_list:
                    status = order.get('sysStatus', 'N/A')
                    status_text = self._get_status_text(status)
                    status_count[status_text] = status_count.get(status_text, 0) + 1
                    try:
                        total_amount += float(order.get('payment', 0))
                    except:
                        pass

                summary += "## 📈 状态统计\n"
                for status, count in status_count.items():
                    summary += f"- **{status}**: {count} 个\n"

                summary += f"\n💰 **总金额**: ¥{total_amount:.2f}\n\n"

                summary += "## 📝 订单列表\n"
                for i, order in enumerate(order_list[:10], 1):
                    tid = order.get('tid', 'N/A')
                    shop = order.get('shopName', order.get('shopId', 'N/A'))
                    sys_status = order.get('sysStatus', 'N/A')
                    status_text = self._get_status_text(sys_status)
                    payment = order.get('payment', '0')

                    summary += f"**{i}.** `{tid}`\n"
                    summary += f"   🏪 {shop} | 📊 {status_text} | 💰 ¥{payment}\n\n"

                if len(order_list) > 10:
                    summary += f"📄 *还有 {len(order_list) - 10} 个订单未显示*\n\n"

                summary += "💡 使用 `查询订单 订单号` 查看详细信息"
                yield event.plain_result(summary)

            elif len(params) >= 3 and params[1] == "时间":
                # 处理日期格式查询
                date_str = params[2]
                start_time = f"{date_str} 00:00:00"
                end_time = f"{date_str} 23:59:59"

                result = self.kuaimai_api.query_orders_by_time(start_time, end_time, "pay_time")

                if not result.get("success", False):
                    yield event.plain_result(f"查询失败: {result.get('msg', '未知错误')}")
                    return

                order_list = result.get("list", [])
                if not order_list:
                    yield event.plain_result(f"{date_str} 暂无订单")
                    return

                summary = f"**{date_str} 的订单 ({len(order_list)}个)**\n\n"
                for i, order in enumerate(order_list[:10], 1):
                    summary += f"{i}. {order.get('tid', 'N/A')} - "
                    sys_status = order.get('sysStatus', 'N/A')
                    status_text = self._get_status_text(sys_status)
                    summary += f"{status_text} - "
                    summary += f"{order.get('payment', '0')}元\n"

                if len(order_list) > 10:
                    summary += f"\n... 还有 {len(order_list) - 10} 个订单"

                summary += f"\n\n使用 `/查询订单 订单号` 查看详细信息"
                yield event.plain_result(summary)

            else:
                # 普通订单号查询 - 显示基本信息包含商品ID、SKUID、所属店铺
                # 平台订单号（TID）
                result = self.kuaimai_api.query_order_by_tid(query_param)
                query_type = "TID"

                # 使用新的format_basic_info方法
                formatted_info = self.kuaimai_api.format_basic_info(result, query_type)
                yield event.plain_result(formatted_info)

        except Exception as e:
            logger.error(f"ID查询失败: {e}")
            yield event.plain_result(f"查询失败: {str(e)}")

    @filter.command("查询商品")
    async def query_item(self, event: AstrMessageEvent):
        """查询单个商品详细信息"""
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        message_str = event.message_str.strip()
        params = message_str.split()

        if len(params) < 2:
            yield event.plain_result(
                "📋 查询商品使用方法\n\n"
                "🔸 **通过平台商家编码查询（推荐）**：\n"
                "• `/查询商品 742261468349`\n\n"
                "🔸 **通过系统商品ID查询**：\n"
                "• `/查询商品 系统ID 622517776007680`\n\n"
                "🔸 **通过平台商品ID查询**：\n"
                "• `/查询商品 平台ID ************ 店铺ID`\n\n"
                "📝 **说明**：\n"
                "• 只显示核心信息：系统商品ID、平台商品ID、平台商家编码、商品标题\n"
                "• 平台商家编码是最常用的查询方式"
            )
            return

        try:
            # 判断查询类型
            if len(params) >= 3 and params[1] == "系统ID":
                # 通过系统主商品ID查询
                sys_item_id = int(params[2])
                logger.info(f"[查询商品] 使用系统ID查询: {sys_item_id}")
                result = self.kuaimai_api.query_single_item(sys_item_id=sys_item_id)
                query_type = f"系统ID: {sys_item_id}"
            elif len(params) >= 4 and params[1] == "平台ID":
                # 通过平台商品ID查询
                platform_id = params[2]
                shop_id = int(params[3])  # 可以是user_id或taobao_id
                logger.info(f"[查询商品] 使用平台ID查询: {platform_id}, 店铺ID: {shop_id}")

                # 先尝试作为taobao_id查询
                result = self.kuaimai_api.query_item_by_platform_id(
                    num_iid=platform_id,
                    taobao_id=shop_id
                )
                query_type = f"平台ID: {platform_id} (店铺ID: {shop_id})"
            else:
                # 通过系统商家编码查询
                outer_id = params[1]
                logger.info(f"[查询商品] 使用系统商家编码查询: {outer_id}")
                result = self.kuaimai_api.query_single_item(outer_id=outer_id)
                query_type = f"商家编码: {outer_id}"

            # 打印API返回结果
            logger.info(f"[查询商品] API返回结果: {result}")

            if not result.get("success", False):
                logger.error(f"[查询商品] 查询失败: {result.get('msg', '未知错误')}")
                yield event.plain_result(f"查询失败: {result.get('msg', '未知错误')}")
                return

            item_data = result.get("item", {})
            if not item_data:
                logger.warning(f"[查询商品] 未找到商品信息")
                yield event.plain_result("未找到商品信息")
                return

            logger.info(f"[查询商品] 获取到商品基本信息: sysItemId={item_data.get('sysItemId')}, outerId={item_data.get('outerId')}, title={item_data.get('title')}")

            # 如果有映射信息，添加到item_data中
            mapping_info = result.get("mapping_info")
            if mapping_info:
                item_data["mapping_info"] = mapping_info
                logger.info(f"[查询商品] 添加映射信息: {mapping_info}")

            # 如果有平台信息，添加到item_data中
            platform_info = result.get("platform_info")
            if platform_info:
                item_data["platform_info"] = platform_info
                logger.info(f"[查询商品] 添加平台信息: {platform_info}")
            else:
                logger.info(f"[查询商品] 未获取到平台信息")

            # 格式化商品信息 - 使用简化格式
            formatted_info = self.kuaimai_api.format_simple_item_info(item_data)
            logger.info(f"[查询商品] 格式化后的商品信息长度: {len(formatted_info)}")

            yield event.plain_result(f"# 🔍 商品查询结果 ({query_type})\n\n{formatted_info}")

        except ValueError:
            yield event.plain_result("数字ID必须是有效的数字")
        except Exception as e:
            logger.error(f"查询商品失败: {e}")
            yield event.plain_result(f"查询商品失败: {str(e)}")

    @filter.command("批量查询商品")
    async def query_multiple_items(self, event: AstrMessageEvent):
        """批量查询商品信息"""
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        message_str = event.message_str.strip()
        command_part = message_str.split(None, 1)

        if len(command_part) < 2:
            yield event.plain_result(
                "📋 批量查询商品使用方法\n\n"
                "🔸 **通过平台商家编码查询（默认）**：\n"
                "• `/批量查询商品 742261468349 742261468350 742261468351`\n"
                "• 或换行输入：\n"
                "```\n"
                "/批量查询商品\n"
                "742261468349\n"
                "742261468350\n"
                "742261468351\n"
                "```\n\n"
                "🔸 **通过系统商品ID查询**：\n"
                "• `/批量查询商品 系统ID 622517776007680 622517776007681`\n\n"
                "🔸 **通过平台商品ID查询**：\n"
                "• `/批量查询商品 平台ID 店铺ID ************ 786591249845`\n\n"
                "📝 **说明**：\n"
                "• 最多支持20个商品ID\n"
                "• 显示信息：系统商品ID、平台商品ID、平台商家编码、系统商家编码、商品标题\n"
                "• 系统商家编码：快麦系统内部编码\n"
                "• 平台商家编码：电商平台的商家编码（通过商品对应关系查询获得）"
            )
            return

        try:
            # 解析商品ID列表
            id_input = command_part[1]
            id_type = "outer_id"  # 默认使用平台商家编码
            user_id = None
            taobao_id = None

            # 检查是否指定了特殊ID类型
            if id_input.startswith("系统ID "):
                id_type = "sys_item_id"
                id_input = id_input[4:]  # 移除"系统ID "前缀
            elif id_input.startswith("平台ID "):
                id_type = "platform_id"
                parts = id_input[4:].split(None, 1)  # 移除"平台ID "前缀并分割
                if len(parts) < 2:
                    yield event.plain_result("使用平台ID查询时，需要提供店铺ID\n格式: `/批量查询商品 平台ID 店铺ID ID1 ID2 ID3`")
                    return

                shop_id = int(parts[0])
                taobao_id = shop_id  # 默认作为平台店铺ID
                id_input = parts[1]

            # 解析ID列表
            if '\n' in id_input:
                item_ids = [line.strip() for line in id_input.split('\n') if line.strip()]
            else:
                item_ids = [id.strip() for id in id_input.split() if id.strip()]

            if not item_ids:
                yield event.plain_result("请提供有效的商品ID")
                return

            if len(item_ids) > 20:
                yield event.plain_result("批量查询最多支持20个商品ID，请减少查询数量")
                return

            yield event.plain_result(f"🔄 开始批量查询 {len(item_ids)} 个商品，请稍候...")

            # 批量查询商品
            results = self.kuaimai_api.query_multiple_items(
                item_ids,
                id_type,
                user_id=user_id,
                taobao_id=taobao_id
            )

            # 格式化结果
            summary = f"# 📦 批量商品查询结果\n\n"
            summary += f"📊 **查询统计**: 总数 {results['total_count']} | 成功 {results['success_count']} | 失败 {results['failed_count']}\n"
            summary += f"🔍 **查询类型**: {results.get('id_type', 'unknown')}\n\n"

            # 成功的商品 - 使用简化格式
            if results['items']:
                summary += "## ✅ 查询成功的商品\n\n"
                for idx, item in enumerate(results['items'], 1):
                    item_data = item['data']
                    summary += f"### {idx}. {item_data.get('title', 'N/A')}\n"

                    # 使用简化的商品信息格式
                    simple_info = self.kuaimai_api.format_simple_item_info(item_data)
                    summary += simple_info + "\n\n"

            # 失败的商品
            if results['failed_items']:
                summary += "## ❌ 查询失败的商品\n\n"
                for idx, failed in enumerate(results['failed_items'], 1):
                    summary += f"{idx}. **ID**: {failed['item_id']} - **错误**: {failed['error']}\n"

            summary += f"\n💡 使用 `/查询商品 商品ID` 查看单个商品的详细信息"
            yield event.plain_result(summary)

        except ValueError as ve:
            yield event.plain_result(f"参数错误: {str(ve)}")
        except Exception as e:
            logger.error(f"批量查询商品失败: {e}")
            yield event.plain_result(f"批量查询商品失败: {str(e)}")

    @filter.command("帮助")
    async def show_help(self, event: AstrMessageEvent):
        """显示插件帮助信息（美化版）"""
        help_text = """
快麦订单查询插件

支持的命令：

1. `/查询订单 订单号`
   - 智能识别平台订单号和系统订单号
   - 自动判断TID/SID类型
   - 返回完整订单详情

2. `/id查询 参数`
   - 支持多种查询方式：
   - `/id查询 订单号` - 查询指定订单（包含商品ID、SKUID、所属店铺）
   - `/id查询 今天` - 查询今天所有订单
   - `/id查询 昨天` - 查询昨天所有订单
   - `/id查询 时间 2024-06-14` - 查询指定日期订单

3. `/查询商品 商品ID`
   - 查询单个商品的详细信息
   - 支持平台商家编码、系统主商品ID和平台商品ID
   - 返回商品名称、价格、规格、状态等完整信息
   - 示例: `/查询商品 ABC123` 或 `/查询商品 系统ID ************`
   - 示例: `/查询商品 平台ID ************ 123456`

4. `/批量查询商品 ID列表`
   - 批量查询多个商品信息
   - 支持平台商家编码、系统ID和平台商品ID
   - 支持空格分隔或换行输入
   - 最多支持20个商品同时查询
   - 显示查询统计和简化商品信息
   - 示例: `/批量查询商品 ABC123 DEF456 GHI789`
   - 示例: `/批量查询商品 平台ID 123456 ID1 ID2 ID3`

5. `/负责人 订单号`
   - 查询订单中商品的负责人信息
   - 支持单个或批量查询
   - 单个查询：显示订单号、负责人、店铺
   - 批量查询：显示简化列表（订单号、负责人、店铺）
   - 支持空格分隔或换行输入多个订单号

6. `/ID负责人 商品ID`
   - 直接通过商品ID查询负责人
   - 支持单个或批量查询
   - 单个查询：显示详细信息
   - 批量查询：显示简化列表（ID、负责人、店铺）
   - 支持空格分隔或换行输入多个ID

7. `/tx 订单号`
   - 生成异常订单通知并发送到钉钉群
   - 自动查询负责人并@提醒
   - 包含订单信息、商品详情、处理建议
   - 自动生成发送格式: tx+订单号

8. `/拉取异常订单`
   - 手动触发异常订单拉取和推送
   - 查询近15日的异常订单
   - 生成Excel报告并推送到钉钉群
   - 需要先开启异常订单推送功能

9. 异常订单推送管理：
   - `/开启异常订单推送本群` - 开启推送功能
   - `/关闭异常订单推送本群` - 关闭推送功能
   - `/查看异常订单推送状态` - 查看当前状态

使用示例：
- `/查询订单 2607071952645719790`
- `/id查询 今天`
- `/id查询 昨天`
- `/id查询 时间 2024-06-14`
- `/查询商品 ABC123` - 单个商品查询（商家编码）
- `/查询商品 系统ID ************` - 通过系统ID查询
- `/查询商品 平台ID ************ 123456` - 通过平台商品ID查询
- `/批量查询商品 ABC123 DEF456 GHI789` - 批量查询（商家编码）
- `/批量查询商品 平台ID 123456 ID1 ID2 ID3` - 平台ID批量查询
- `/负责人 2607071952645719790` - 单个查询
- `/负责人 2607071952645719790 2607071952645719791` - 批量查询
- `/ID负责人 ************` - 单个查询
- `/ID负责人 123456 789012 345678` - 批量查询
- `/tx 2607071952645719790`
- `/拉取异常订单`
- `/开启异常订单推送本群`

功能特点：
- 智能订单号识别
- 批量订单概览
- 详细订单信息
- 灵活时间查询
- 商品ID和SKUID查询
- 单个和批量商品信息查询
- 负责人信息查询（支持批量）
- 异常订单通知生成

简单实用，专注核心功能！
        """
        yield event.plain_result(help_text.strip())

    @filter.command("负责人")
    async def query_order_manager(self, event: AstrMessageEvent):
        """查询订单负责人信息 - 支持单个或多个订单号查询"""
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        if not self.bi_api:
            yield event.plain_result("BI API未初始化，无法查询负责人信息，请检查配置")
            return

        message_str = event.message_str.strip()
        command_part = message_str.split(None, 1)

        if len(command_part) < 2:
            yield event.plain_result(
                "请提供订单号\n\n"
                "使用方法:\n"
                "• `/负责人 订单号` - 查询单个订单\n"
                "• `/负责人 订单号1 订单号2 订单号3` - 查询多个订单（空格分隔）\n"
                "• 或者换行输入多个订单号：\n"
                "/负责人\n"
                "2607071952645719790\n"
                "2607071952645719791\n"
                "2607071952645719792"
            )
            return

        # 解析订单号列表
        order_numbers = []
        order_input = command_part[1]
        if '\n' in order_input:
            order_numbers = [line.strip() for line in order_input.split('\n') if line.strip()]
        else:
            order_numbers = [order.strip() for order in order_input.split() if order.strip()]

        if not order_numbers:
            yield event.plain_result("请提供有效的订单号")
            return

        try:
            # 批量查询订单负责人
            summary = f"负责人查询结果 ({len(order_numbers)}个订单)\n\n"

            for i, order_number in enumerate(order_numbers, 1):
                try:
                    # 直接通过订单号查询订单详情
                    result = self.kuaimai_api.query_order_by_tid(order_number)

                    if not result.get("success", False):
                        summary += f"{i}. 订单号: {order_number}\n   查询失败: {result.get('msg', '未知错误')}\n\n"
                        continue

                    order_list = result.get("list", [])
                    if not order_list:
                        summary += f"{i}. 订单号: {order_number}\n   未找到订单信息\n\n"
                        continue

                    order_info = order_list[0]

                    # 提取商品ID
                    goods_ids = []
                    for item in order_info.get("orders", []):
                        goods_id = item.get('numIid') or item.get('oid') or item.get('id')
                        if goods_id:
                            goods_ids.append(str(goods_id))

                    if not goods_ids:
                        summary += f"{i}. 订单号: {order_number}\n   订单中无商品信息\n\n"
                        continue

                    # 查询负责人
                    bi_result = self.bi_api.query_goods_manager(goods_ids)
                    manager_list = []
                    if bi_result and bi_result.get('success', False):
                        data = bi_result.get('data')
                        if data is None:
                            # data为None代表BI中暂无该商品负责人
                            manager_info = 'BI中暂无该商品负责人信息'
                        elif isinstance(data, dict):
                            for goods in data.get('list', []):
                                managers = goods.get('managerList', [])
                                if managers:
                                    manager_names = [m.get('userName', 'N/A') for m in managers]
                                    manager_list.extend(manager_names)
                            if manager_list:
                                manager_info = '、'.join(set(manager_list))  # 去重
                            else:
                                manager_info = 'BI中暂无该商品负责人信息'
                        else:
                            manager_info = 'BI中暂无该商品负责人信息'
                    else:
                        manager_info = 'BI中暂无该商品负责人信息'

                    # 获取店铺信息
                    shop_name = order_info.get('shopName', 'N/A')

                    summary += f"{i}. 订单号: {order_number}\n   负责人: {manager_info}\n   所属店铺: {shop_name}\n\n"

                except Exception as order_e:
                    summary += f"{i}. 订单号: {order_number}\n   查询异常: {str(order_e)}\n\n"

            yield event.plain_result(summary)

        except Exception as e:
            logger.error(f"查询订单负责人失败: {e}")
            yield event.plain_result(f"查询订单负责人失败: {str(e)}")

    @filter.command("ID负责人")
    async def query_manager_by_id(self, event: AstrMessageEvent):
        """通过商品ID查询负责人 - 支持单个或多个ID查询（新版）"""
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        if not self.bi_api:
            yield event.plain_result("BI API未初始化，无法查询负责人信息，请检查配置")
            return

        message_str = event.message_str.strip()
        command_part = message_str.split(None, 1)
        if len(command_part) < 2:
            yield event.plain_result(
                "请提供商品ID\n\n"
                "使用方法:\n"
                "• `/ID负责人 商品ID` - 查询单个商品\n"
                "• `/ID负责人 ID1 ID2 ID3` - 查询多个商品（空格分隔）\n"
                "• 或者换行输入多个ID：\n"
                "/ID负责人\n"
                "123456\n"
                "789012\n"
                "345678"
            )
            return
        # 解析商品ID列表
        goods_ids = []
        id_input = command_part[1]
        if '\n' in id_input:
            goods_ids = [line.strip() for line in id_input.split('\n') if line.strip()]
        else:
            goods_ids = [id.strip() for id in id_input.split() if id.strip()]
        if not goods_ids:
            yield event.plain_result("请提供有效的商品ID")
            return
        try:
            # 添加详细日志
            logger.info(f"[ID负责人] 开始查询，商品ID列表: {goods_ids}")
            logger.info(f"[ID负责人] BI API状态: {self.bi_api is not None}")

            # 再次检查 bi_api 状态
            if self.bi_api is None:
                error_msg = "BI API在运行时变为None，请检查配置和初始化"
                logger.error(f"[ID负责人] {error_msg}")
                yield event.plain_result(error_msg)
                return

            logger.info(f"[ID负责人] 调用 BI API 查询负责人...")
            bi_result = self.bi_api.query_goods_manager(goods_ids)
            logger.info(f"[ID负责人] BI API 返回结果: {bi_result}")

            summary = f"负责人查询结果 ({len(goods_ids)}个商品)\n\n"

            if bi_result is None:
                logger.error("[ID负责人] BI API 返回 None")
                summary += "BI API 返回空结果"
            elif not isinstance(bi_result, dict):
                logger.error(f"[ID负责人] BI API 返回类型错误: {type(bi_result)}")
                summary += f"BI API 返回类型错误: {type(bi_result)}"
            elif bi_result.get('success', False):
                logger.info("[ID负责人] BI API 查询成功")
                data = bi_result.get('data', {})
                logger.info(f"[ID负责人] 数据内容: {data}")

                if data is None:
                    logger.info("[ID负责人] data 字段为 None，BI中暂无该商品负责人信息")
                    # 当data为None时，为每个查询的商品ID显示"BI中暂无该商品负责人信息"
                    for i, goods_id in enumerate(goods_ids, 1):
                        summary += f"{i}. 商品ID: {goods_id}\n   负责人: BI中暂无该商品负责人信息\n   所属店铺: N/A\n\n"
                elif not isinstance(data, dict):
                    logger.warning(f"[ID负责人] data 字段类型错误: {type(data)}")
                    summary += f"数据字段类型错误: {type(data)}"
                else:
                    goods_list = data.get('list', [])
                    logger.info(f"[ID负责人] 商品列表长度: {len(goods_list)}")

                    if not goods_list:
                        summary += "未找到商品信息（商品可能不存在或无负责人数据）"
                    else:
                        for i, goods in enumerate(goods_list, 1):
                            logger.info(f"[ID负责人] 处理第{i}个商品: {goods}")

                            if goods is None:
                                logger.warning(f"[ID负责人] 第{i}个商品为None")
                                summary += f"{i}. 商品数据为空\n\n"
                                continue

                            try:
                                managers = goods.get('managerList', [])
                                goods_id = goods.get('goodsId', 'N/A')
                                shop_name = goods.get('shopName', 'N/A')

                                logger.info(f"[ID负责人] 商品{goods_id}的负责人列表: {managers}")

                                if managers:
                                    manager_names = []
                                    for m in managers:
                                        if m and isinstance(m, dict):
                                            manager_names.append(m.get('userName', 'N/A'))
                                        else:
                                            logger.warning(f"[ID负责人] 负责人数据格式错误: {m}")
                                    manager_info = '、'.join(manager_names) if manager_names else 'BI中暂无该商品负责人信息'
                                else:
                                    manager_info = 'BI中暂无该商品负责人信息'

                                summary += f"{i}. 商品ID: {goods_id}\n   负责人: {manager_info}\n   所属店铺: {shop_name}\n\n"

                            except Exception as goods_e:
                                logger.error(f"[ID负责人] 处理第{i}个商品时出错: {goods_e}")
                                summary += f"{i}. 商品处理出错: {str(goods_e)}\n\n"
            else:
                error_msg = bi_result.get('msg', '未知错误')
                logger.error(f"[ID负责人] BI API 查询失败: {error_msg}")
                summary += f"查询失败: {error_msg}"

            logger.info(f"[ID负责人] 最终返回结果长度: {len(summary)}")
            yield event.plain_result(summary)

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            logger.error(f"[ID负责人] 查询商品负责人失败: {e}")
            logger.error(f"[ID负责人] 详细错误信息: {error_detail}")
            yield event.plain_result(f"查询商品负责人失败: {str(e)}\n\n详细错误信息已记录到日志中")
    @filter.command("tx")
    async def send_exception_notice(self, event: AstrMessageEvent):
        """生成异常订单通知并发送到钉钉群"""
        if not self.kuaimai_api:
            yield event.plain_result("快麦API未初始化，请检查配置")
            return

        message_str = event.message_str.strip()
        params = message_str.split()

        if len(params) < 2:
            yield event.plain_result(
                "请提供订单号\n\n"                "使用方法:\n"
                "• `/tx 订单号` - 生成异常订单通知并发送到钉钉群（自动@负责人）\n"
                "• 通知格式: tx+订单号"
            )
            return

        order_number = params[1]

        try:
            # 先查询订单信息
            order_result = self.kuaimai_api.query_order_by_tid(order_number)

            if not order_result.get("success", False):
                yield event.plain_result(f"查询订单失败: {order_result.get('msg', '未知错误')}")
                return

            order_list = order_result.get("list", [])
            if not order_list:
                yield event.plain_result("未找到订单信息")
                return

            order_info = order_list[0]            # 生成异常订单通知
            notice_content = self.kuaimai_api.generate_exception_notice(order_info)
            
            # 从通知内容中提取负责人信息
            manager_name = None
            at_user_id = None
            
            # 从notice_content中提取负责人姓名
            import re
            manager_match = re.search(r'👤 \*\*订单负责人：\*\*\n\s+`([^`]+)`', notice_content)
            if manager_match:
                manager_name = manager_match.group(1)
                # 如果负责人不是"BI中暂无该商品"，则查询userid
                if manager_name != "BI中暂无该商品" and self.dingtalk_api:
                    try:
                        user_info = self.dingtalk_api.search_user_by_name(manager_name)
                        if user_info:
                            at_user_id = user_info.get('userid')
                    except Exception as e:
                        logger.warning(f"查询用户ID失败: {e}")
            
            # 发送到钉钉群
            dingtalk_sent = False
            if self.dingtalk_api:
                try:
                    # 发送完整的通知内容到钉钉群
                    dingtalk_sent = self.dingtalk_api.send_robot_message(
                        content=notice_content,
                        at_user_id=at_user_id
                    )
                    if dingtalk_sent:
                        at_info = f"（已@{manager_name}）" if at_user_id else "（未找到用户ID）"
                        yield event.plain_result(f"✅ 异常订单通知已发送到钉钉群{at_info}")
                    else:
                        yield event.plain_result(f"❌ 钉钉消息发送失败\n\n{notice_content}")
                except Exception as e:
                    logger.error(f"发送钉钉消息失败: {e}")
                    yield event.plain_result(f"❌ 钉钉消息发送异常: {str(e)}\n\n{notice_content}")
            else:
                yield event.plain_result(f"⚠️ 钉钉API未配置，仅显示通知内容\n\n{notice_content}")

        except Exception as e:
            logger.error(f"生成异常订单通知失败: {e}")
            yield event.plain_result(f"生成异常订单通知失败: {str(e)}")

    @filter.command("测试异常订单推送")
    async def test_exception_report(self, event: AstrMessageEvent):
        """
        手动测试异常订单报告推送到钉钉群（仅Excel文件）
        """
        if not get_exception_push_enabled():
            yield event.plain_result("❌ 异常订单推送未开启，请先发送「开启异常订单推送本群」指令")
            return

        try:
            # 重置防重复机制，允许手动测试
            exception_reporter.reset_report_time()

            # 调用异常订单报告任务
            _, exception_orders = exception_reporter.exception_report_job()

            if exception_orders:
                yield event.plain_result(f"✅ 异常订单Excel报告已推送到钉钉群！\n📊 共发现 {len(exception_orders)} 个异常订单\n📋 已发送Excel文件")
            else:
                yield event.plain_result("✅ 报告已生成！\n📊 近15日暂无异常订单")
        except Exception as e:
            yield event.plain_result(f"❌ 推送失败: {e}")

    @filter.command("重置异常订单报告时间")
    async def reset_exception_report_time(self, event: AstrMessageEvent):
        """重置异常订单报告时间，用于调试"""
        try:
            exception_reporter.reset_report_time()
            yield event.plain_result("✅ 异常订单报告时间已重置\n📝 现在可以重新发送定时报告")
        except Exception as e:
            yield event.plain_result(f"❌ 重置失败: {e}")

    @filter.command("拉取异常订单")
    async def manual_exception_report(self, event: AstrMessageEvent):
        """
        手动触发异常订单拉取和推送
        """
        if not get_exception_push_enabled():
            yield event.plain_result("❌ 异常订单推送未开启，请先发送「开启异常订单推送本群」指令")
            return

        try:
            yield event.plain_result("🔄 开始拉取异常订单数据，请稍候...")

            # 调用异常订单报告任务
            _, exception_orders = exception_reporter.exception_report_job()

            if exception_orders:
                yield event.plain_result(f"✅ 异常订单拉取完成！\n📊 共发现 {len(exception_orders)} 个异常订单\n📋 Excel报告已推送到钉钉群")
            else:
                yield event.plain_result("✅ 异常订单拉取完成！\n📊 近15日暂无异常订单")
        except Exception as e:
            logger.error(f"手动拉取异常订单失败: {e}")
            yield event.plain_result(f"❌ 拉取异常订单失败: {e}")

    @filter.command("开启异常订单推送本群")
    async def enable_exception_push(self, event: AstrMessageEvent):
        if write_exception_push_config(True):
            yield event.plain_result("✅ 已开启本群异常订单推送\n📝 配置已保存到文件，重启后仍然有效")
        else:
            yield event.plain_result("❌ 开启推送失败，配置文件写入错误")

    @filter.command("关闭异常订单推送本群")
    async def disable_exception_push(self, event: AstrMessageEvent):
        if write_exception_push_config(False):
            yield event.plain_result("🚫 已关闭本群异常订单推送\n📝 配置已保存到文件，重启后仍然有效")
        else:
            yield event.plain_result("❌ 关闭推送失败，配置文件写入错误")

    @filter.command("查看异常订单推送状态")
    async def check_exception_push_status(self, event: AstrMessageEvent):
        enabled = get_exception_push_enabled()
        status_text = "✅ 已开启" if enabled else "🚫 已关闭"
        yield event.plain_result(f"📊 异常订单推送状态：{status_text}\n📝 配置保存在文件中，重启后仍然有效")

# 配置文件操作函数
def read_exception_push_config():
    """
    从配置文件读取异常订单推送开关状态
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        return config_data.get('exception_push', {}).get('enabled', False)
    except Exception as e:
        print(f"读取推送配置失败: {e}")
        return False

def write_exception_push_config(enabled):
    """
    将异常订单推送开关状态写入配置文件
    """
    try:
        # 读取当前配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # 更新推送配置
        if 'exception_push' not in config_data:
            config_data['exception_push'] = {}
        config_data['exception_push']['enabled'] = enabled

        # 写回配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, sort_keys=False)

        print(f"推送配置已保存: enabled={enabled}")
        return True
    except Exception as e:
        print(f"保存推送配置失败: {e}")
        return False

def get_exception_push_enabled():
    """
    获取当前异常订单推送开关状态
    """
    return read_exception_push_config()

# 修改异常报告推送逻辑
# import exception_reporter  # 已在文件顶部导入
old_send_to_dingtalk = exception_reporter.send_to_dingtalk
old_send_excel_to_dingtalk = exception_reporter.send_excel_to_dingtalk  # 直接引用原始函数，避免循环

def send_to_dingtalk_with_switch(report_md):
    if not get_exception_push_enabled():
        print("异常订单推送已关闭，本群不推送")
        return
    old_send_to_dingtalk(report_md)

def send_excel_to_dingtalk_with_switch(excel_file_path, exception_count=0):
    if not get_exception_push_enabled():
        print("异常订单Excel推送已关闭，本群不推送")
        return
    old_send_excel_to_dingtalk(excel_file_path, exception_count)

exception_reporter.send_to_dingtalk = send_to_dingtalk_with_switch
exception_reporter.send_excel_to_dingtalk_with_switch = send_excel_to_dingtalk_with_switch

# 定时任务已移至 exception_reporter.py 中统一管理
# 避免重复的定时任务系统
