#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
钉钉模块初始化文件
"""

# 导入主要的类和函数
try:
    from .dingtalk_sdk import DingTalkSDK
    from .config import DingTalkConfig
    from .dingtalk_api import DingTalkAPI
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from dingtalk_sdk import DingTalkSDK
        from config import DingTalkConfig
        from dingtalk_api import DingTalkAPI
    except ImportError:
        pass

__all__ = ['DingTalkSDK', 'DingTalkConfig', 'DingTalkAPI']
