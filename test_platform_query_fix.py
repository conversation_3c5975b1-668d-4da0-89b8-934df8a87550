#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试平台商品ID查询修复
"""

import sys
import os
import logging

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_platform_query_method():
    """测试平台商品ID查询方法"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="INFO"
        )
        
        print("🔍 测试平台商品ID查询方法:")
        print("=" * 60)
        
        # 测试新的平台查询方法
        print("\n📋 测试 query_platform_item_mapping 方法:")
        result = api.query_platform_item_mapping(
            platform_item_ids=["786591249844"],
            taobao_id=123456
        )
        print(f"查询结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_mapping_query_restrictions():
    """测试商品对应关系查询限制"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="INFO"
        )
        
        print("\n🔍 测试商品对应关系查询限制:")
        print("=" * 60)
        
        # 测试使用 outerIds 查询（应该失败）
        print("\n📋 测试使用 outerIds 查询（应该失败）:")
        result = api.query_item_outer_id_mapping(outer_ids=["742261468349"])
        print(f"查询结果: {result}")
        
        if not result.get("success", False):
            print("✅ 正确拒绝了 outerIds 查询")
        else:
            print("❌ 错误地允许了 outerIds 查询")
            return False
        
        # 测试使用 numIidList 查询（需要店铺ID）
        print("\n📋 测试使用 numIidList 查询（缺少店铺ID，应该失败）:")
        result = api.query_item_outer_id_mapping(num_iid_list=["786591249844"])
        print(f"查询结果: {result}")
        
        if not result.get("success", False):
            print("✅ 正确要求提供店铺ID")
        else:
            print("❌ 错误地允许了缺少店铺ID的查询")
            return False
        
        # 测试正确的 numIidList 查询
        print("\n📋 测试正确的 numIidList 查询:")
        result = api.query_item_outer_id_mapping(
            num_iid_list=["786591249844"],
            taobao_id=123456
        )
        print(f"查询结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_single_item_query():
    """测试单个商品查询"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="INFO"
        )
        
        print("\n🔍 测试单个商品查询:")
        print("=" * 60)
        
        # 测试系统商家编码查询（不会触发平台信息查询）
        print("\n📋 测试系统商家编码查询:")
        result = api.query_single_item(outer_id="742261468349")
        print(f"查询结果: {result}")
        
        # 检查是否有平台信息查询的日志
        print("✅ 单个商品查询不再尝试使用 outerIds 查询平台信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试平台商品ID查询修复...")
    print("=" * 80)
    
    tests = [
        ("平台查询方法", test_platform_query_method),
        ("商品对应关系查询限制", test_mapping_query_restrictions),
        ("单个商品查询", test_single_item_query)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！平台商品ID查询修复成功！")
        print("\n📋 修复内容:")
        print("• 商品对应关系查询固定使用 numIidList")
        print("• 不再支持 outerIds 查询")
        print("• 添加了专门的平台商品ID查询方法")
        print("• 单个商品查询不再尝试使用 outerIds 查询平台信息")
        print("• 添加了详细的日志输出")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
