# -*- coding: utf-8 -*-
"""
快麦API操作类
实现订单查询和订单详情返回功能
"""
import hashlib
import hmac
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
import requests
import logging

class KuaimaiAPI:
    """
    快麦API客户端类
    """
    def __init__(self, app_key: str, secret: str, session: str, base_url: str = "https://gw.superboss.cc/router", log_level: str = "INFO"):
        self.app_key = app_key
        self.secret = secret
        self.session = session
        self.base_url = base_url
        self.log_level = log_level.upper()
        self.logger = logging.getLogger("KuaimaiAPI")
        if self.log_level == "DEBUG":
            self.logger.setLevel(logging.DEBUG)
        else:
            self.logger.setLevel(logging.INFO)

    def _generate_timestamp(self) -> str:
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _generate_signature(self, params: Dict[str, Any], sign_method: str = "hmac") -> str:
        filtered_params = {k: str(v) for k, v in params.items() if v is not None and k != 'sign'}
        sorted_params = sorted(filtered_params.items())
        str_params = "".join([f"{k}{v}" for k, v in sorted_params])
        if sign_method == 'md5':
            sign_str = self.secret + str_params + self.secret
            return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
        elif sign_method == 'hmac':
            return hmac.new(
                self.secret.encode('utf-8'),
                str_params.encode('utf-8'),
                digestmod=hashlib.md5
            ).hexdigest().upper()
        elif sign_method == 'hmac-sha256':
            return hmac.new(
                self.secret.encode('utf-8'),
                str_params.encode('utf-8'),
                digestmod=hashlib.sha256
            ).hexdigest().upper()
        else:
            raise ValueError(f"不支持的签名方法: {sign_method}")

    def _build_common_params(self, method: str, sign_method: str = "hmac") -> Dict[str, str]:
        return {
            "method": method,
            "appKey": self.app_key,
            "timestamp": self._generate_timestamp(),
            "format": "json",
            "version": "1.0",
            "sign_method": sign_method, "session": self.session
        }

    def _make_request(self, method: str, business_params: Dict[str, Any] = None, sign_method: str = "hmac") -> Dict[str, Any]:
        try:
            params = self._build_common_params(method, sign_method)
            if business_params:
                params.update(business_params)
            params["sign"] = self._generate_signature(params, sign_method)
            if self.log_level == "DEBUG":
                self.logger.debug(f"[KuaimaiAPI] 请求接口: {self.base_url}")
                self.logger.debug(f"[KuaimaiAPI] 请求参数: {params}")
            response = requests.post(self.base_url, data=params, timeout=30)
            response.raise_for_status()
            result = response.json()
            if self.log_level == "DEBUG":
                self.logger.debug(f"[KuaimaiAPI] 返回结果: {result}")
            return result
        except requests.exceptions.RequestException as e:
            self.logger.error(f"[KuaimaiAPI] 请求失败: {str(e)}")
            return {"success": False, "code": "REQUEST_ERROR", "msg": f"请求失败: {str(e)}", "trace_id": ""}
        except json.JSONDecodeError as e:
            self.logger.error(f"[KuaimaiAPI] 响应解析失败: {str(e)}")
            return {"success": False, "code": "JSON_ERROR", "msg": f"响应解析失败: {str(e)}", "trace_id": ""}
        except Exception as e:
            self.logger.error(f"[KuaimaiAPI] 未知错误: {str(e)}")
            return {"success": False, "code": "UNKNOWN_ERROR", "msg": f"未知错误: {str(e)}", "trace_id": ""}

    def query_orders_by_time(self, start_time: str, end_time: str, time_type: str = "pay_time", user_ids: str = None, page_size: int = 20, page_no: int = 1) -> Dict[str, Any]:
        business_params = {
            "timeType": time_type,
            "startTime": start_time,
            "endTime": end_time,
            "pageSize": page_size,
            "pageNo": page_no
        }
        if user_ids:
            business_params["userIds"] = user_ids
        return self._make_request("erp.trade.outstock.simple.query", business_params)

    def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        business_params = {"refreshToken": refresh_token}
        return self._make_request("open.token.refresh", business_params)

    def query_order_by_tid(self, tid: str) -> Dict[str, Any]:
        """通过订单号查询订单详情"""
        business_params = {"tid": tid}
        return self._make_request("erp.trade.outstock.simple.query", business_params)

    def quick_query(self, order_number: str) -> Dict[str, Any]:
        return self.query_order_by_tid(order_number)

    def get_today_orders(self, time_type: str = "pay_time") -> Dict[str, Any]:
        today = datetime.now()
        start_time = today.strftime("%Y-%m-%d 00:00:00")
        end_time = today.strftime("%Y-%m-%d 23:59:59")
        return self.query_orders_by_time(start_time, end_time, time_type)

    def get_yesterday_orders(self, time_type: str = "pay_time") -> Dict[str, Any]:
        from datetime import timedelta
        yesterday = datetime.now() - timedelta(days=1)
        start_time = yesterday.strftime("%Y-%m-%d 00:00:00")
        end_time = yesterday.strftime("%Y-%m-%d 23:59:59")
        return self.query_orders_by_time(start_time, end_time, time_type)

    def format_full_order_markdown(self, order: dict) -> str:
        """
        美化输出订单信息，全中文格式化
        """
        from datetime import datetime, timezone, timedelta

        # 字段中文映射
        field_map = {
            'tid': '订单号',
            'shopName': '店铺名称',
            'shopId': '店铺ID',
            'sysStatus': '订单状态',
            'tradeStatus': '交易状态',
            'orderType': '订单类型',
            'created': '下单时间',
            'payTime': '付款时间',
            'consignTime': '发货时间',
            'endTime': '完成时间',
            'receiverName': '收件人',
            'receiverMobile': '联系电话',
            'receiverState': '省份',
            'receiverCity': '城市',
            'receiverDistrict': '区县',
            'receiverAddress': '详细地址',
            'payment': '实付金额',
            'totalFee': '订单总额',
            'discountFee': '优惠金额',
            'postFee': '运费',
            'buyerMessage': '买家留言',
            'sellerMemo': '卖家备注',
            'sysMemo': '系统备注',
            'logisticsNumber': '运单号',
            'logisticsCompany': '快递公司',
            'orders': '商品明细',
            'gifts': '赠品信息',
            'exceptions': '异常标签'
        }

        # 状态映射
        status_map = {
            "WAIT_BUYER_PAY": "待付款",
            "WAIT_AUDIT": "待审核",
            "WAIT_FINANCE_AUDIT": "待财审",
            "FINISHED_AUDIT": "审核完成",
            "WAIT_EXPRESS_PRINT": "待打印快递单",
            "WAIT_PACKAGE": "待打包",
            "WAIT_WEIGHT": "待称重",
            "WAIT_SEND_GOODS": "待发货",
            "WAIT_DEST_SEND_GOODS": "待供销商发货",
            "SELLER_SEND_GOODS": "卖家已发货",
            "FINISHED": "交易完成",
            "CLOSED": "交易关闭",
        }

        # 时间格式化函数
        def format_time(timestamp):
            if not timestamp:
                return "无"
            try:
                if isinstance(timestamp, (int, float)):
                    dt = datetime.fromtimestamp(timestamp/1000, tz=timezone.utc) + timedelta(hours=8)
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    return str(timestamp)
            except:
                return str(timestamp)

        lines = ["# 📋 订单详情报告\n"]

        # 基本信息
        lines.append("## 📌 基本信息")
        basic_fields = ['tid', 'shopName', 'sysStatus', 'orderType']
        for field in basic_fields:
            if field in order:
                value = order[field]
                if field == 'sysStatus':
                    value = status_map.get(value, value)
                field_name = field_map.get(field, field)
                lines.append(f"- **{field_name}**: {value}")

        # 时间信息
        lines.append("\n## ⏰ 时间信息")
        time_fields = ['created', 'payTime', 'consignTime', 'endTime']
        for field in time_fields:
            if field in order:
                field_name = field_map.get(field, field)
                formatted_time = format_time(order[field])
                lines.append(f"- **{field_name}**: {formatted_time}")

        # 收件人信息（脱敏处理）
        lines.append("\n## 👤 收件人信息")
        receiver_fields = ['receiverName', 'receiverMobile', 'receiverState', 'receiverCity', 'receiverDistrict', 'receiverAddress']
        for field in receiver_fields:
            if field in order and order[field]:
                value = order[field]
                field_name = field_map.get(field, field)

                # 脱敏处理
                if field == 'receiverName' and len(str(value)) > 1:
                    value = str(value)[0] + '*' * (len(str(value)) - 1)
                elif field == 'receiverMobile' and len(str(value)) > 7:
                    value = str(value)[:3] + '****' + str(value)[-4:]
                elif field == 'receiverAddress' and len(str(value)) > 10:
                    value = str(value)[:6] + '***' + str(value)[-4:]

                lines.append(f"- **{field_name}**: {value}")

        # 金额信息
        lines.append("\n## 💰 金额信息")
        money_fields = ['payment', 'totalFee', 'discountFee', 'postFee']
        for field in money_fields:
            if field in order and order[field]:
                field_name = field_map.get(field, field)
                value = f"¥{order[field]}"
                lines.append(f"- **{field_name}**: {value}")

        # 商品明细
        if 'orders' in order and order['orders']:
            lines.append("\n## 🛍️ 商品明细")
            for idx, item in enumerate(order['orders'], 1):
                lines.append(f"### 商品 {idx}")
                lines.append(f"- **商品ID**: {item.get('numIid', item.get('oid', 'N/A'))}")
                lines.append(f"- **商品名称**: {item.get('title', 'N/A')}")
                lines.append(f"- **规格**: {item.get('skuPropertiesName', 'N/A')}")
                lines.append(f"- **数量**: {item.get('num', 'N/A')}")
                lines.append(f"- **单价**: ¥{item.get('price', 'N/A')}")
                lines.append(f"- **总价**: ¥{item.get('totalFee', 'N/A')}")

        # 赠品信息
        if 'gifts' in order and order['gifts']:
            lines.append("\n## 🎁 赠品信息")
            for idx, gift in enumerate(order['gifts'], 1):
                lines.append(f"- **赠品{idx}**: {gift.get('title', 'N/A')} × {gift.get('num', 1)}")

        # 物流信息
        if order.get('logisticsNumber') or order.get('logisticsCompany'):
            lines.append("\n## 🚚 物流信息")
            if order.get('logisticsCompany'):
                lines.append(f"- **快递公司**: {order['logisticsCompany']}")
            if order.get('logisticsNumber'):
                lines.append(f"- **运单号**: {order['logisticsNumber']}")

        # 备注信息
        memo_fields = ['buyerMessage', 'sellerMemo', 'sysMemo']
        memo_content = []
        for field in memo_fields:
            if field in order and order[field]:
                field_name = field_map.get(field, field)
                memo_content.append(f"- **{field_name}**: {order[field]}")

        if memo_content:
            lines.append("\n## 📝 备注信息")
            lines.extend(memo_content)

        # 异常信息
        if 'exceptions' in order and order['exceptions']:
            lines.append("\n## ⚠️ 异常信息")
            exceptions = order['exceptions']
            if isinstance(exceptions, list):
                for exc in exceptions:
                    lines.append(f"- 🔸 {exc}")
            else:
                lines.append(f"- 🔸 {exceptions}")

        lines.append(f"\n---\n📅 查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return '\n'.join(lines)

    def format_basic_info(self, result: Dict[str, Any], query_type: str = "TID") -> str:
        """格式化基本订单信息，包含商品ID、SKUID、所属店铺"""
        if not result.get("success", False):
            return f"查询失败: {result.get('msg', '未知错误')}"

        order_list = result.get("list", [])
        if not order_list:
            return "未找到订单信息"

        order = order_list[0]
        lines = [f"# 📋 订单基本信息 ({query_type})\n"]

        # 基本信息
        lines.append("## 📌 基本信息")
        lines.append(f"- **订单号**: {order.get('tid', 'N/A')}")
        lines.append(f"- **店铺**: {order.get('shopName', 'N/A')}")
        lines.append(f"- **状态**: {order.get('sysStatus', 'N/A')}")

        # 商品信息
        orders_items = order.get('orders', [])
        if orders_items:
            lines.append("\n## 🛍️ 商品信息")
            for idx, item in enumerate(orders_items, 1):
                goods_id = item.get('numIid') or item.get('oid') or item.get('id') or 'N/A'
                sku_id = item.get('skuId', 'N/A')
                title = item.get('title', 'N/A')
                num = item.get('num', 1)

                lines.append(f"### 商品{idx}")
                lines.append(f"- **商品ID**: {goods_id}")
                lines.append(f"- **SKUID**: {sku_id}")
                lines.append(f"- **商品名称**: {title}")
                lines.append(f"- **数量**: {num}")
                lines.append(f"- **所属店铺**: {order.get('shopName', 'N/A')}")
                lines.append("")

        lines.append(f"---\n📅 查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return '\n'.join(lines)

    def generate_exception_notice(self, order_info: Dict[str, Any]) -> str:
        """生成异常订单通知内容"""
        try:
            # 导入BI API来查询负责人
            from ..bi.bi_api import BIAPI
            from .. import exception_reporter

            tid = order_info.get('tid', 'N/A')
            shop_name = order_info.get('shopName', 'N/A')

            # 获取商品信息和负责人
            orders_items = order_info.get('orders', [])
            manager_info = "BI中暂无该商品负责人信息"

            if orders_items and exception_reporter.bi_api:
                goods_ids = []
                for item in orders_items:
                    goods_id = item.get('numIid') or item.get('oid') or item.get('id')
                    if goods_id:
                        goods_ids.append(str(goods_id))

                if goods_ids:
                    try:
                        bi_result = exception_reporter.bi_api.query_goods_manager(goods_ids)
                        if bi_result and bi_result.get('success', False):
                            data = bi_result.get('data', {})
                            manager_list = []
                            if isinstance(data, dict):
                                for goods in data.get('list', []):
                                    managers = goods.get('managerList', [])
                                    if managers:
                                        manager_names = [m.get('userName', 'N/A') for m in managers]
                                        manager_list.extend(manager_names)
                            if manager_list:
                                manager_info = '、'.join(set(manager_list))  # 去重
                    except Exception as e:
                        print(f"查询负责人失败: {e}")

            # 生成通知内容
            notice = f"""🚨 **异常订单通知**

📋 **订单信息：**
    `{tid}`

🏪 **所属店铺：**
    `{shop_name}`

👤 **订单负责人：**
    `{manager_info}`

⚠️ **处理建议：**
    请及时查看订单详情，处理相关异常问题

📝 **发送格式：**
    tx{tid}

---
📅 通知时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            return notice

        except Exception as e:
            return f"生成异常订单通知失败: {str(e)}"

    def change_warehouse(self, sids: List[str], warehouse_id: int, force: bool = False) -> Dict[str, Any]:
        """
        修改订单仓库

        Args:
            sids: 系统订单号列表
            warehouse_id: 仓库ID
            force: 库存不足时是否强制换仓库

        Returns:
            Dict: API响应结果
        """
        business_params = {
            "sids": ",".join(sids),
            "warehouseId": warehouse_id,
            "force": force
        }
        return self._make_request("erp.trade.change.warehouse", business_params)

    def query_warehouses(self, name: str = None, warehouse_id: int = None) -> Dict[str, Any]:
        """
        查询仓库信息

        Args:
            name: 仓库名称（可选）
            warehouse_id: 仓库ID（可选）

        Returns:
            Dict: API响应结果
        """
        business_params = {}
        if name:
            business_params["name"] = name
        if warehouse_id:
            business_params["warehouseId"] = warehouse_id

        return self._make_request("erp.warehouse.query", business_params)

    def query_item_outer_id_mapping(self, outer_ids: List[str] = None, num_iid_list: List[str] = None,
                                   user_id: int = None, taobao_id: int = None) -> Dict[str, Any]:
        """
        查询商品对应关系（平台商品ID与商家编码的对应关系）

        Args:
            outer_ids: 系统商家编码列表（与num_iid_list二选一必填）
            num_iid_list: 平台商品ID列表（与outer_ids二选一必填）
            user_id: 店铺编码（若选择平台商品ID，则与taobao_id二选一必填）
            taobao_id: 平台店铺ID（若选择平台商品ID，则与user_id二选一必填）

        Returns:
            Dict: API响应结果，包含商品对应关系信息
        """
        if not outer_ids and not num_iid_list:
            return {"success": False, "code": "PARAM_ERROR", "msg": "outer_ids和num_iid_list至少需要提供一个", "trace_id": ""}

        if num_iid_list and not user_id and not taobao_id:
            return {"success": False, "code": "PARAM_ERROR", "msg": "使用平台商品ID查询时，user_id和taobao_id至少需要提供一个", "trace_id": ""}

        business_params = {}

        if outer_ids:
            business_params["outerIds"] = ",".join(outer_ids)
        elif num_iid_list:
            business_params["numIidList"] = ",".join(num_iid_list)
            if user_id:
                business_params["userId"] = user_id
            if taobao_id:
                business_params["taobaoId"] = taobao_id

        return self._make_request("erp.item.outerid.list.get", business_params)

    def query_single_item(self, outer_id: str = None, sys_item_id: int = None, whether_return_purchase: int = 0) -> Dict[str, Any]:
        """
        查询单个商品明细信息

        Args:
            outer_id: 平台商家编码（同系统主商品ID二选一，优先取系统主商品ID）
            sys_item_id: 系统主商品ID（同平台商家编码二选一，优先取系统主商品ID）
            whether_return_purchase: 是否返回采购链接，0.否 1.是，默认不返回

        Returns:
            Dict: API响应结果，包含商品详细信息
        """
        if not outer_id and not sys_item_id:
            return {"success": False, "code": "PARAM_ERROR", "msg": "outer_id和sys_item_id至少需要提供一个", "trace_id": ""}

        business_params = {
            "whetherReturnPurchase": whether_return_purchase
        }

        if sys_item_id:
            business_params["sysItemId"] = sys_item_id
        elif outer_id:
            business_params["outerId"] = outer_id

        return self._make_request("item.single.get", business_params)

    def query_item_by_platform_id(self, num_iid: str, user_id: int = None, taobao_id: int = None,
                                 whether_return_purchase: int = 0) -> Dict[str, Any]:
        """
        通过平台商品ID查询商品详细信息（先查对应关系，再查商品详情）

        Args:
            num_iid: 平台商品ID
            user_id: 店铺编码（与taobao_id二选一必填）
            taobao_id: 平台店铺ID（与user_id二选一必填）
            whether_return_purchase: 是否返回采购链接，0.否 1.是，默认不返回

        Returns:
            Dict: API响应结果，包含商品详细信息
        """
        if not user_id and not taobao_id:
            return {"success": False, "code": "PARAM_ERROR", "msg": "user_id和taobao_id至少需要提供一个", "trace_id": ""}

        # 第一步：查询商品对应关系，获取商家编码
        mapping_result = self.query_item_outer_id_mapping(
            num_iid_list=[num_iid],
            user_id=user_id,
            taobao_id=taobao_id
        )

        if not mapping_result.get("success", False):
            return {
                "success": False,
                "code": "MAPPING_ERROR",
                "msg": f"查询商品对应关系失败: {mapping_result.get('msg', '未知错误')}",
                "trace_id": mapping_result.get("trace_id", "")
            }

        # 从对应关系结果中提取商家编码
        item_outer_id_infos = mapping_result.get("itemOuterIdInfos", [])
        if not item_outer_id_infos:
            return {
                "success": False,
                "code": "NO_MAPPING",
                "msg": f"未找到平台商品ID {num_iid} 对应的商家编码",
                "trace_id": mapping_result.get("trace_id", "")
            }

        # 获取第一个匹配的商家编码
        outer_id = item_outer_id_infos[0].get("outerId")
        if not outer_id:
            return {
                "success": False,
                "code": "NO_OUTER_ID",
                "msg": f"平台商品ID {num_iid} 对应的商家编码为空",
                "trace_id": mapping_result.get("trace_id", "")
            }

        # 第二步：使用商家编码查询商品详细信息
        item_result = self.query_single_item(outer_id=outer_id, whether_return_purchase=whether_return_purchase)

        # 在结果中添加映射信息
        if item_result.get("success", False):
            item_result["mapping_info"] = {
                "platform_id": num_iid,
                "outer_id": outer_id,
                "user_id": user_id,
                "taobao_id": taobao_id
            }

        return item_result

    def query_multiple_items(self, item_ids: List[str], id_type: str = "outer_id", whether_return_purchase: int = 0,
                           user_id: int = None, taobao_id: int = None) -> Dict[str, Any]:
        """
        批量查询商品明细信息

        Args:
            item_ids: 商品ID列表
            id_type: ID类型，可选值: "outer_id"（平台商家编码）, "sys_item_id"（系统主商品ID）, "platform_id"（平台商品ID）
            whether_return_purchase: 是否返回采购链接，0.否 1.是，默认不返回
            user_id: 店铺编码（当id_type为platform_id时需要，与taobao_id二选一）
            taobao_id: 平台店铺ID（当id_type为platform_id时需要，与user_id二选一）

        Returns:
            Dict: 包含所有查询结果的字典
        """
        results = {
            "success": True,
            "items": [],
            "failed_items": [],
            "total_count": len(item_ids),
            "success_count": 0,
            "failed_count": 0,
            "id_type": id_type
        }

        # 如果是平台商品ID类型，先批量查询对应关系
        if id_type == "platform_id":
            if not user_id and not taobao_id:
                results["success"] = False
                results["failed_items"] = [{
                    "item_id": "ALL",
                    "error": "使用平台商品ID查询时，user_id和taobao_id至少需要提供一个"
                }]
                results["failed_count"] = len(item_ids)
                return results

            # 批量查询对应关系
            mapping_result = self.query_item_outer_id_mapping(
                num_iid_list=item_ids,
                user_id=user_id,
                taobao_id=taobao_id
            )

            if not mapping_result.get("success", False):
                results["success"] = False
                results["failed_items"] = [{
                    "item_id": "ALL",
                    "error": f"查询商品对应关系失败: {mapping_result.get('msg', '未知错误')}"
                }]
                results["failed_count"] = len(item_ids)
                return results

            # 构建平台ID到商家编码的映射
            id_mapping = {}
            item_outer_id_infos = mapping_result.get("itemOuterIdInfos", [])
            for info in item_outer_id_infos:
                platform_id = info.get("numIid")
                outer_id = info.get("outerId")
                if platform_id and outer_id:
                    id_mapping[platform_id] = outer_id

            # 使用映射后的商家编码查询商品详情
            for item_id in item_ids:
                try:
                    outer_id = id_mapping.get(item_id)
                    if not outer_id:
                        results["failed_items"].append({
                            "item_id": item_id,
                            "error": f"未找到平台商品ID {item_id} 对应的商家编码"
                        })
                        results["failed_count"] += 1
                        continue

                    result = self.query_single_item(outer_id=outer_id, whether_return_purchase=whether_return_purchase)

                    if result.get("success", False):
                        item_data = result.get("item", {})
                        # 添加映射信息
                        item_data["mapping_info"] = {
                            "platform_id": item_id,
                            "outer_id": outer_id,
                            "user_id": user_id,
                            "taobao_id": taobao_id
                        }
                        results["items"].append({
                            "item_id": item_id,
                            "data": item_data
                        })
                        results["success_count"] += 1
                    else:
                        results["failed_items"].append({
                            "item_id": item_id,
                            "error": result.get("msg", "未知错误")
                        })
                        results["failed_count"] += 1

                except Exception as e:
                    results["failed_items"].append({
                        "item_id": item_id,
                        "error": f"查询异常: {str(e)}"
                    })
                    results["failed_count"] += 1
        else:
            # 原有的逻辑：直接使用商家编码或系统ID查询
            for item_id in item_ids:
                try:
                    if id_type == "sys_item_id":
                        result = self.query_single_item(sys_item_id=int(item_id), whether_return_purchase=whether_return_purchase)
                    else:
                        result = self.query_single_item(outer_id=item_id, whether_return_purchase=whether_return_purchase)

                    if result.get("success", False):
                        results["items"].append({
                            "item_id": item_id,
                            "data": result.get("item", {})
                        })
                        results["success_count"] += 1
                    else:
                        results["failed_items"].append({
                            "item_id": item_id,
                            "error": result.get("msg", "未知错误")
                        })
                        results["failed_count"] += 1

                except Exception as e:
                    results["failed_items"].append({
                        "item_id": item_id,
                        "error": f"查询异常: {str(e)}"
                    })
                    results["failed_count"] += 1

        if results["failed_count"] > 0 and results["success_count"] == 0:
            results["success"] = False

        return results

    def format_item_info(self, item_data: Dict[str, Any], show_detailed: bool = True) -> str:
        """
        格式化商品信息为Markdown格式

        Args:
            item_data: 商品数据
            show_detailed: 是否显示详细信息

        Returns:
            str: 格式化后的商品信息
        """
        if not item_data:
            return "商品数据为空"

        lines = []

        if show_detailed:
            lines.append("# 📦 商品详细信息\n")

            # 基本信息 - 只显示核心信息
            lines.append("## 📌 基本信息")
            lines.append(f"- **系统商品ID**: {item_data.get('sysItemId', 'N/A')}")
            lines.append(f"- **平台商家编码**: {item_data.get('outerId', 'N/A')}")

            # 添加平台商品ID（如果有映射信息）
            mapping_info = item_data.get('mapping_info')
            if mapping_info and mapping_info.get('platform_id'):
                lines.append(f"- **平台商品ID**: {mapping_info.get('platform_id', 'N/A')}")

            lines.append(f"- **商品标题**: {item_data.get('title', 'N/A')}")

            # 其他详细信息（可选显示）
            lines.append(f"- **商品简称**: {item_data.get('shortTitle', 'N/A')}")
            lines.append(f"- **商品类型**: {self._get_item_type_text(item_data.get('type'), item_data.get('typeTag'))}")
            lines.append(f"- **商品类目**: {item_data.get('catId', 'N/A')}")
            lines.append(f"- **单位**: {item_data.get('unit', 'N/A')}")
            lines.append(f"- **条形码**: {item_data.get('barcode', 'N/A')}")

            # 状态信息
            lines.append("\n## 📊 状态信息")
            lines.append(f"- **可用状态**: {'启用' if item_data.get('activeStatus') == 1 else '停用'}")
            lines.append(f"- **是否含SKU**: {'是' if item_data.get('isSkuItem') == 1 else '否'}")
            lines.append(f"- **是否虚拟商品**: {'是' if item_data.get('isVirtual') == 1 else '否'}")
            lines.append(f"- **是否关联供应商**: {'是' if item_data.get('hasSupplier') == 1 else '否'}")
            lines.append(f"- **是否标记为赠品**: {'是' if item_data.get('makeGift') else '否'}")

            # 价格和规格信息
            lines.append("\n## 💰 价格信息")
            lines.append(f"- **成本价**: ¥{item_data.get('purchasePrice', 'N/A')}")
            lines.append(f"- **销售价**: ¥{item_data.get('priceOutput', 'N/A')}")

            lines.append("\n## 📏 规格信息")
            lines.append(f"- **重量**: {item_data.get('weight', 'N/A')} kg")
            lines.append(f"- **长**: {item_data.get('x', 'N/A')} cm")
            lines.append(f"- **宽**: {item_data.get('y', 'N/A')} cm")
            lines.append(f"- **高**: {item_data.get('z', 'N/A')} cm")
            lines.append(f"- **箱规**: {item_data.get('boxnum', 'N/A')}")

            # 分类信息
            seller_cats = item_data.get('sellerCats', [])
            if seller_cats:
                lines.append("\n## 📂 分类信息")
                for cat in seller_cats:
                    lines.append(f"- **{cat.get('name', 'N/A')}** (ID: {cat.get('id', 'N/A')}, 编码: {cat.get('cid', 'N/A')})")

            # 套件明细
            suit_single_list = item_data.get('suitSingleList', [])
            if suit_single_list:
                lines.append("\n## 🔧 套件明细")
                for idx, suit in enumerate(suit_single_list, 1):
                    lines.append(f"### 套件组件 {idx}")
                    lines.append(f"- **平台商家编码**: {suit.get('outerId', 'N/A')}")
                    lines.append(f"- **商品名称**: {suit.get('title', 'N/A')}")
                    lines.append(f"- **组合比例**: {suit.get('ratio', 'N/A')}")
                    lines.append(f"- **规格商家编码**: {suit.get('skuOuterId', 'N/A')}")
                    lines.append(f"- **规格属性**: {suit.get('propertiesName', 'N/A')}")

            # 时间信息
            lines.append("\n## ⏰ 时间信息")
            lines.append(f"- **创建时间**: {self._format_timestamp(item_data.get('created'))}")
            lines.append(f"- **更新时间**: {self._format_timestamp(item_data.get('modified'))}")
            lines.append(f"- **上架日期**: {self._format_timestamp(item_data.get('listTime'))}")

        else:
            # 简化信息 - 只显示核心字段
            lines.append(f"**{item_data.get('title', 'N/A')}**")
            lines.append(f"- **系统商品ID**: {item_data.get('sysItemId', 'N/A')}")

            # 添加平台商品ID（如果有映射信息）
            mapping_info = item_data.get('mapping_info')
            if mapping_info and mapping_info.get('platform_id'):
                lines.append(f"- **平台商品ID**: {mapping_info.get('platform_id', 'N/A')}")

            lines.append(f"- **平台商家编码**: {item_data.get('outerId', 'N/A')}")

        return '\n'.join(lines)

    def format_simple_item_info(self, item_data: Dict[str, Any]) -> str:
        """
        格式化简化的商品信息，只显示核心字段

        Args:
            item_data: 商品数据

        Returns:
            str: 格式化后的简化商品信息
        """
        if not item_data:
            return "商品数据为空"

        lines = []
        lines.append(f"- **系统商品ID**: {item_data.get('sysItemId', 'N/A')}")

        # 添加平台商品ID（如果有映射信息）
        mapping_info = item_data.get('mapping_info')
        if mapping_info and mapping_info.get('platform_id'):
            lines.append(f"- **平台商品ID**: {mapping_info.get('platform_id', 'N/A')}")

        lines.append(f"- **平台商家编码**: {item_data.get('outerId', 'N/A')}")
        lines.append(f"- **商品标题**: {item_data.get('title', 'N/A')}")

        return '\n'.join(lines)

    def _get_item_type_text(self, item_type: str, type_tag: int = None) -> str:
        """获取商品类型文本描述"""
        type_map = {
            "0": "普通商品",
            "1": "SKU套件",
            "2": "纯套件",
            "3": "包材商品"
        }

        base_type = type_map.get(str(item_type), f"未知类型({item_type})")

        if item_type == "0" and type_tag is not None:
            tag_map = {
                0: "正常商品",
                1: "SKU加工商品",
                2: "纯加工商品",
                3: "含SKU组合装商品",
                4: "不含SKU组合装商品"
            }
            tag_text = tag_map.get(type_tag, f"未知标签({type_tag})")
            return f"{base_type} - {tag_text}"

        return base_type

    def _format_timestamp(self, timestamp) -> str:
        """格式化时间戳"""
        if not timestamp:
            return "N/A"
        try:
            if isinstance(timestamp, (int, float)):
                dt = datetime.fromtimestamp(timestamp/1000)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return str(timestamp)
        except:
            return str(timestamp)