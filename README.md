# 快麦API订单查询插件

汐娅专属的Bot插件，集成了快麦API订单查询功能。

## 功能特性

- 🔍 订单查询（支持TID平台订单号）
- 📋 支持多种订单号格式（纯数字和带连字符）
- 📅 支持时间范围查询
- 📊 订单详情格式化显示（全中文Markdown格式）
- 🔐 安全的API签名认证
- ⚡ 异步处理，响应快速
- 📈 异常订单报告（Markdown + Excel双格式）
- 🤖 钉钉群推送（支持开关控制）
- 📋 Excel报告自动生成和发送
- 🎨 美化Excel样式（表头、边框、字体、颜色）
- 🌏 全中文界面，用户体验友好

## 支持的订单号格式

- `2607071952645719790` - 纯数字订单号
- `250615-501104064822347` - 带连字符的订单号

## 支持的命令

| 命令 | 功能 | 示例 |
|------|------|------|
| `/查询订单 订单号` | 查询订单详情 | `/查询订单 2607071952645719790` |
| `/id查询 订单号` | 查询订单基本信息 | `/id查询 250615-501104064822347` |
| `/id查询 今天` | 查询今天的订单 | `/id查询 今天` |
| `/id查询 昨天` | 查询昨天的订单 | `/id查询 昨天` |
| `/id查询 时间 2024-06-14` | 查询指定日期订单 | `/id查询 时间 2024-06-14` |
| `/负责人 订单号` | 查询订单负责人（支持批量） | `/负责人 2607071952645719790` |
| `/ID负责人 商品ID` | 查询商品负责人（支持批量） | `/ID负责人 761250659987` |
| `/帮助` | 显示帮助信息 | `/帮助` |
| `测试异常订单报告` | 手动推送异常订单报告 | `测试异常订单报告` |
| `重置异常订单报告时间` | 重置防重复机制 | `重置异常订单报告时间` |
| `开启异常订单推送本群` | 开启异常订单推送 | `开启异常订单推送本群` |
| `关闭异常订单推送本群` | 关闭异常订单推送 | `关闭异常订单推送本群` |
| `查看异常订单推送状态` | 查看当前推送状态 | `查看异常订单推送状态` |

## 时间类型说明

- `created` - 下单时间
- `pay_time` - 付款时间（默认）
- `consign_time` - 发货时间
- `audit_time` - 审核时间
- `upd_time` - 修改时间

## 安装配置

### 1. 依赖安装

```bash
pip install -r requirements.txt
```

或手动安装：

```bash
pip install requests pandas openpyxl PyYAML
```

### 2. 配置API密钥

复制 `config_example.yaml` 为 `config.yaml` 并填入您的API参数：

```yaml
kuaimai:
  app_key: "您的AppKey"
  secret: "您的Secret"
  session: "您的SessionToken"

dingtalk:
  app_key: "您的钉钉AppKey"
  app_secret: "您的钉钉AppSecret"
  webhook_url: "您的钉钉机器人Webhook地址"
  robot_code: "您的机器人编码"  # 发送文件需要
  open_conversation_id: "您的群会话ID"  # 发送文件需要

bi:
  token: "您的BI系统Token"
  cookie: "您的BI系统Cookie"

# 异常订单推送设置
exception_push:
  enabled: false  # 是否开启异常订单推送，默认关闭
```

### 3. 重启AstrBot

配置完成后重启AstrBot以使插件生效。

## API文档参考

本插件基于快麦开放平台API开发，支持以下功能：

- **订单查询API**: `erp.trade.outstock.simple.query`
- **商品查询API**: `item.single.get`
- **会话刷新API**: `open.token.refresh`
- **签名算法**: 支持HMAC-MD5、MD5、HMAC-SHA256

## 返回信息格式

查询订单时，插件会返回包含以下信息的详细报告：

#### 📋 订单详情报告
- **📌 基本信息**: 订单号、店铺名称、订单状态、订单类型
- **⏰ 时间信息**: 下单时间、付款时间、发货时间、完成时间
- **👤 收件人信息**: 姓名、电话、地址（脱敏处理）
- **💰 金额信息**: 实付金额、订单总额、优惠金额、运费
- **🛍️ 商品明细**: 商品ID、名称、规格、数量、单价、总价
- **🎁 赠品信息**: 赠品名称和数量（如有）
- **🚚 物流信息**: 快递公司、运单号（如有）
- **📝 备注信息**: 买家留言、卖家备注、系统备注
- **⚠️ 异常信息**: 异常标签（如有）

#### 📊 订单列表汇总
- **状态统计**: 各状态订单数量统计
- **金额汇总**: 当日/昨日订单总金额
- **详细列表**: 订单号、店铺、状态、金额

## 异常订单报告功能

### 功能说明
- 📊 自动扫描近15天的异常订单
- 📋 生成Excel格式报告并发送到钉钉群
- 📢 发送@全体成员提醒消息，告知异常订单数量
- ⏰ 支持定时推送（每天9:00、16:00）
- 🔐 支持推送开关控制，配置持久化保存
- ⚡ 优化生成速度：批量查询负责人信息，避免重复数据拉取
- 🗑️ 自动清理：Excel文件发送完成后自动删除
- 💾 配置文件存储：推送开关状态保存在配置文件中，重启后仍然有效
- 🎨 Excel美化样式：专业表头、边框、字体、颜色、列宽、筛选器

### 异常类型
- **商品未匹配** (EX_UNALLOCATED)
- **库存不足** (EX_INSUFFICIENT)
- **风控订单** (EX_RISK_ORDER)
- **退款订单** (EX_REFUND) - 自动过滤

### Excel报告内容
- 序号、订单号、店铺名称
- 下单时间、当前状态、异常标签
- 商品ID、商品名称、负责人信息（来自BI系统）
- 专业表格样式，支持筛选和排序

### 使用方法
1. 发送 `开启异常订单推送本群` 开启推送功能（配置会保存到文件）
2. 发送 `测试异常订单报告` 手动触发Excel报告推送
3. 发送 `查看异常订单推送状态` 查看当前推送状态
4. 发送 `关闭异常订单推送本群` 关闭推送功能（配置会保存到文件）

### 推送内容
每次推送包含：
1. **Excel文件**: 详细的异常订单数据表格
2. **@全体提醒**: 文字消息提醒异常订单数量，@所有群成员

### 配置持久化
- 推送开关状态保存在 `config.yaml` 文件的 `exception_push.enabled` 字段中
- 重启AstrBot后推送状态仍然有效，无需重新设置
- 支持手动编辑配置文件来修改推送状态

## 安全说明

- ✅ 使用HTTPS加密传输
- ✅ 支持多种签名算法验证
- ✅ 自动处理时间戳防重放
- ✅ 敏感信息脱敏显示
- ⚠️ 请妥善保管API密钥信息

## 注意事项

1. **Session Token有效期**: 默认30天，需定期刷新
2. **API限流**: 遵循快麦平台的API调用频率限制
3. **时间格式**: 查询时间需使用 `yyyy-MM-dd HH:mm:ss` 格式
4. **时区设置**: 时间参数使用GMT+8时区
5. **数据权限**: 仅返回有权限访问的订单信息

## 错误处理

插件内置了完善的错误处理机制：

- 网络请求异常自动重试
- API错误码解析和友好提示
- 配置错误检查和提示
- 参数格式验证

## 版本历史

- **v1.4.9**: 修复异常订单汇总重复推送问题，添加防重复机制，确保每个时间段只发送一次
- **v1.4.8**: 修复定时任务时间，调整为每天9:00和16:00执行，优化BI API数据处理逻辑
- **v1.4.7**: 修复查询失败问题，完善快麦API缺失方法，增强负责人查询支持批量操作
- **v1.4.6**: 修复BI API空值检查问题，增强配置文件格式验证，提升系统稳定性
- **v1.4.5**: 修复钉钉SDK导入问题，使用新版钉钉SDK，清理过时文件上传方案
- **v1.4.4**: 新增@全体成员提醒功能，发送Excel文件后自动推送提醒消息
- **v1.4.3**: 优化推送策略，注释掉文字报告，仅发送Excel文件到钉钉群
- **v1.4.2**: 美化Excel报告样式，优化查询订单返回格式，全中文界面
- **v1.4.1**: 优化推送开关控制，配置持久化保存到文件，重启后仍然有效
- **v1.4.0**: 新增异常订单报告功能，支持Markdown+Excel双格式推送到钉钉群
- **v1.3.2**: 调整订单号处理逻辑，保留完整订单号格式，修复缩进问题
- **v1.3.1**: 优化订单查询逻辑，支持带连字符订单号格式
- **v1.3.0**: 集成快麦API订单查询功能
- **v1.0.0**: 基础Hello World功能

## 技术支持

如遇问题，请联系插件作者或查看：
- 快麦开放平台文档
- AstrBot插件开发文档

---

**作者**: Soulter
**版本**: v1.4.9
**更新时间**: 2025年7月11日
