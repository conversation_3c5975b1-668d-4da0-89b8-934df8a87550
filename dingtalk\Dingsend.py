import requests
import time
from pathlib import Path
from typing import Dict, Optional
import urllib3
import logging
import json
from datetime import datetime

# 禁用SSL验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_dingtalk_token(appkey: str, appsecret: str, token_cache: Optional[Dict] = None) -> str:
    """
    获取钉钉access_token（带简易缓存机制）

    参数:
        appkey: 应用唯一标识
        appsecret: 应用密钥
        token_cache: 可选的缓存字典 {'access_token': str, 'expires_at': float}

    返回:
        access_token字符串
    """
    logger.info(" 开始获取钉钉access_token")

    # 检查缓存
    if token_cache and token_cache.get('access_token') and time.time() < token_cache.get('expires_at', 0) - 30:
        logger.info(" 使用缓存中的有效token")
        return token_cache['access_token']

    url = "https://oapi.dingtalk.com/gettoken"
    params = {"appkey": appkey, "appsecret": appsecret}

    try:
        logger.info(f" 请求获取token接口: {url}")
        response = requests.get(
            url,
            params=params,
            verify=False,  # 禁用SSL验证
            timeout=10
        )
        logger.debug(f" 响应状态码: {response.status_code}")

        result = response.json()
        logger.debug(f" 完整响应: {result}")

        if result.get('errcode') != 0:
            error_msg = f"获取token失败: {result.get('errmsg')}  (错误码: {result.get('errcode')})"
            logger.error(error_msg)
            raise Exception(error_msg)

        if token_cache is not None:
            token_cache.update({
                'access_token': result['access_token'],
                'expires_at': time.time() + result['expires_in']
            })
            logger.info(" 已更新token缓存")

        logger.info(" 成功获取access_token")
        return result['access_token']

    except requests.exceptions.RequestException as e:
        error_msg = f"请求token接口异常: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def upload_dingtalk_file(access_token: str, file_path: Path) -> Dict:
    """
    上传文件到钉钉服务器

    参数:
        access_token: 调用接口凭证
        file_path: 要上传的文件路径

    返回:
        包含media_id等信息的字典
    """
    logger.info(f" 开始上传文件: {file_path}")

    url = "https://oapi.dingtalk.com/media/upload"

    try:
        if not file_path.exists():
            error_msg = f"文件不存在: {file_path}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        file_size = file_path.stat().st_size / (1024 * 1024)  # MB
        logger.info(f" 文件大小: {file_size:.2f}MB")

        with open(file_path, 'rb') as f:
            files = {'media': (file_path.name, f)}
            params = {'access_token': access_token, 'type': 'file'}

            logger.info(f" 开始上传到钉钉服务器 (类型: file)")
            response = requests.post(
                url,
                params=params,
                files=files,
                verify=False,  # 禁用SSL验证
                timeout=30
            )
            logger.debug(f" 响应状态码: {response.status_code}")

            result = response.json()
            logger.debug(f" 完整响应: {result}")

            if result.get('errcode', -1) != 0:
                error_msg = f"上传失败: {result.get('errmsg')}  (错误码: {result.get('errcode')})"
                logger.error(error_msg)
                raise Exception(error_msg)

            logger.info(f" 上传成功，获取到media_id: {result['media_id']}")
            return result

    except Exception as e:
        error_msg = f"上传过程中发生错误: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def send_dingtalk_file(
        appkey: str,
        appsecret: str,
        robot_code: str,
        open_conversation_id: str,
        file_path: str,
        token_cache: Optional[Dict] = None
) -> Dict:
    """
    发送文件到钉钉群（完整流程）

    参数:
        appkey: 应用唯一标识
        appsecret: 应用密钥
        robot_code: 机器人编码
        open_conversation_id: 群会话ID
        file_path: 要发送的文件路径
        token_cache: 可选的token缓存字典

    返回:
        包含发送结果的字典
    """
    logger.info(" 开始发送文件到钉钉群")
    logger.info(f" 参数: robot_code={robot_code}, conversation_id={open_conversation_id}")

    file_path = Path(file_path)
    try:
        # 1. 获取access_token
        logger.info(" 步骤1/3: 获取access_token")
        access_token = get_dingtalk_token(appkey, appsecret, token_cache)

        # 2. 上传文件
        logger.info(" 步骤2/3: 上传文件")
        upload_result = upload_dingtalk_file(access_token, file_path)
        media_id = upload_result['media_id']

        # 3. 发送文件消息
        logger.info(" 步骤3/3: 发送消息")
        url = "https://api.dingtalk.com/v1.0/robot/groupMessages/send"
        headers = {
            "x-acs-dingtalk-access-token": access_token,
            "Content-Type": "application/json"
        }
        payload = {
            "msgKey": "sampleFile",
            "msgParam": json.dumps({
                "mediaId": media_id,
                "fileName": file_path.name,
                "fileType": "xlsx"
            }),
            "openConversationId": open_conversation_id,
            "robotCode": robot_code
        }

        logger.debug(f" 请求payload: {payload}")
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            verify=False,  # 禁用SSL验证
            timeout=10
        )
        logger.debug(f" 响应状态码: {response.status_code}")

        result = response.json()
        logger.debug(f" 完整响应: {result}")

        if result.get('errcode', 0) != 0 or 'processQueryKey' not in result:
            error_msg = f"消息发送失败: {result.get('errmsg')}  (错误码: {result.get('errcode')})"
            logger.error(error_msg)
            raise Exception(error_msg)

        logger.info(f" 消息发送成功，processQueryKey: {result['processQueryKey']}")
        return result

    except Exception as e:
        error_msg = f"发送文件过程中发生错误: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def send_dingtalk_image(
        appkey: str,
        appsecret: str,
        robot_code: str,
        open_conversation_id: str,
        photo_url: str,
        token_cache: Optional[Dict] = None
) -> Dict:
    """
    发送图片消息到钉钉群（整合版）

    参数:
        appkey: 应用唯一标识
        appsecret: 应用密钥
        robot_code: 机器人编码
        open_conversation_id: 群会话ID
        photo_url: 图片URL地址
        token_cache: 可选的token缓存字典

    返回:
        包含发送结果的字典，含processQueryKey等字段

    示例:
        >>> result = send_dingtalk_image(
                "your_appkey",
                "your_appsecret",
                "robot_123",
                "cid123",
                "https://example.com/image.jpg"
            )
    """
    # 日志记录初始化信息
    logger.info(f"[{datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}] 开始图片消息发送流程")
    logger.debug(f" 目标会话: {open_conversation_id[:6]}... 机器人: {robot_code[:4]}...")

    try:
        # 阶段1：认证鉴权
        access_token = get_dingtalk_token(appkey, appsecret, token_cache)

        # 阶段2：消息构建与发送
        payload = {
            "msgKey": "sampleImageMsg",
            "msgParam": json.dumps({"photoURL": photo_url}),
            "openConversationId": open_conversation_id,
            "robotCode": robot_code
        }

        response = requests.post(
            url="https://api.dingtalk.com/v1.0/robot/groupMessages/send",
            headers={
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            },
            json=payload,
            timeout=10
        )

        # 阶段3：结果处理
        result = response.json()
        if response.status_code != 200 or result.get('errcode', 0) != 0:
            raise DingTalkAPIError(
                f"API返回错误: {result.get('errmsg')}",
                code=result.get('errcode'),
                response=response
            )

        logger.info(f" 消息已投递 (processQueryKey: {result['processQueryKey']})")
        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }

    except requests.exceptions.Timeout:
        error_msg = "请求超时：钉钉API响应超过10秒"
        logger.error(error_msg)
        raise

    except json.JSONDecodeError:
        error_msg = "响应解析失败：返回的不是有效JSON"
        logger.error(error_msg)
        raise

    except Exception as e:
        logger.error(f" 未捕获的异常: {str(e)}", exc_info=True)
        raise DingTalkSendError(f"消息发送失败: {str(e)}") from e


class DingTalkAPIError(Exception):
    """钉钉API返回的错误"""

    def __init__(self, message, code=None, response=None):
        super().__init__(message)
        self.code = code
        self.response = response


class DingTalkSendError(Exception):
    """消息发送过程中的通用错误"""
