# -*- coding: utf-8 -*-
"""
BI相关接口操作
"""
import requests
import logging
from typing import List, Dict, Any

class BIAPI:
    def __init__(self, token: str, cookie: str, log_level: str = "INFO"):
        self.token = token
        self.cookie = cookie
        self.base_url = "https://biprodapi.ecbis.cn/prod/goodsMember/getPageGoodsManagerList"
        self.log_level = log_level.upper()
        self.logger = logging.getLogger("BIAPI")
        if self.log_level == "DEBUG":
            self.logger.setLevel(logging.DEBUG)
        else:
            self.logger.setLevel(logging.INFO)

    def query_goods_manager(self, goods_ids: List[str]) -> Dict[str, Any]:
        headers = {
            "bi-token": self.token,
            "Cookie": self.cookie,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "biprodapi.ecbis.cn",
            "Connection": "keep-alive"
        }
        payload = {
            "channelKey": "all",
            "keyWordList": goods_ids,
            "managerType": 0,
            "queryType": "1",
            "status": "1",
            "isValid": 0,
            "isOnJob": 1,
            "orderName": "cjsj",
            "orderSort": "desc",
            "cateType": "0",
            "pageNo": 1,
            "pageNum": 50
        }
        try:
            if self.log_level == "DEBUG":
                self.logger.debug(f"[BIAPI] 请求接口: {self.base_url}")
                self.logger.debug(f"[BIAPI] 请求参数: {payload}")
            response = requests.post(self.base_url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            result = response.json()
            if self.log_level == "DEBUG":
                self.logger.debug(f"[BIAPI] 返回结果: {result}")
            return result
        except Exception as e:
            self.logger.error(f"[BIAPI] 请求失败: {str(e)}")
            return {"success": False, "msg": f"请求失败: {str(e)}", "data": None} 