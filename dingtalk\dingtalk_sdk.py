#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
钉钉SDK - 完整功能实现
支持新版SDK的accessToken获取、文件上传、文件发送等功能
"""

import requests
import json
import os
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class AccessTokenInfo:
    """访问令牌信息"""
    access_token: str
    expire_in: int
    created_at: float

    @property
    def is_expired(self) -> bool:
        """检查token是否过期"""
        return time.time() - self.created_at >= self.expire_in - 300  # 提前5分钟刷新


@dataclass
class UploadInfo:
    """文件上传信息"""
    upload_key: str
    storage_driver: str
    protocol: str
    resource_urls: List[str]
    headers: Dict[str, str]
    expiration_seconds: int
    region: str


@dataclass
class FileInfo:
    """文件信息"""
    id: str
    space_id: str
    parent_id: str
    type: str
    name: str
    size: int
    path: str
    version: int
    status: str
    extension: str
    creator_id: str
    modifier_id: str
    create_time: str
    modified_time: str
    uuid: str
    partition_type: str
    storage_driver: str


@dataclass
class ConversationFileInfo:
    """会话文件信息"""
    id: str
    conversation_id: str
    space_id: str
    parent_id: str
    type: str
    name: str
    size: int
    path: str
    version: int
    status: str
    extension: str
    creator_id: str
    modifier_id: str
    create_time: str
    modified_time: str
    uuid: str


class DingTalkSDK:
    """钉钉完整SDK"""

    def __init__(self, app_key: Optional[str] = None, app_secret: Optional[str] = None,
                 access_token: Optional[str] = None, base_url: str = "https://api.dingtalk.com"):
        """
        初始化SDK

        Args:
            app_key: 应用的Client ID (原AppKey)
            app_secret: 应用的Client Secret (原AppSecret)
            access_token: 直接提供的访问凭证（可选）
            base_url: API基础URL
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })

        # 访问令牌缓存
        self._token_info: Optional[AccessTokenInfo] = None

        # 如果直接提供了access_token，则使用它
        if access_token:
            self._token_info = AccessTokenInfo(
                access_token=access_token,
                expire_in=7200,  # 默认2小时
                created_at=time.time()
            )
            self._update_session_token(access_token)

    def _update_session_token(self, access_token: str):
        """更新session中的token"""
        self.session.headers['x-acs-dingtalk-access-token'] = access_token

    def get_access_token(self, force_refresh: bool = False) -> str:
        """
        获取访问令牌

        Args:
            force_refresh: 是否强制刷新token

        Returns:
            str: 访问令牌

        Raises:
            Exception: 获取token失败时抛出异常
        """
        if not self.app_key or not self.app_secret:
            if self._token_info and not self._token_info.is_expired:
                return self._token_info.access_token
            else:
                raise Exception("未提供app_key和app_secret，且当前token已过期")

        # 检查是否需要刷新token
        if not force_refresh and self._token_info and not self._token_info.is_expired:
            return self._token_info.access_token

        url = f"{self.base_url}/v1.0/oauth2/accessToken"
        data = {
            "appKey": self.app_key,
            "appSecret": self.app_secret
        }

        response = requests.post(url, json=data)

        if response.status_code != 200:
            raise Exception(f"获取accessToken失败: {response.status_code} - {response.text}")

        result = response.json()
        access_token = result.get("accessToken")
        expire_in = result.get("expireIn", 7200)

        if not access_token:
            raise Exception("获取accessToken失败: 响应中没有accessToken字段")

        # 缓存token信息
        self._token_info = AccessTokenInfo(
            access_token=access_token,
            expire_in=expire_in,
            created_at=time.time()
        )

        # 更新session中的token
        self._update_session_token(access_token)

        return access_token

    def ensure_valid_token(self):
        """确保当前token有效"""
        if not self._token_info or self._token_info.is_expired:
            self.get_access_token(force_refresh=True)

    def get_user_info(self, user_id: str, language: str = "zh_CN") -> Dict[str, Any]:
        """
        获取用户详情信息（使用旧版SDK接口）

        Args:
            user_id: 用户ID
            language: 通讯录语言，zh_CN（中文）或en_US（英文）

        Returns:
            Dict: 用户信息，包含unionid等字段

        Raises:
            Exception: 获取用户信息失败时抛出异常
        """
        # 确保token有效
        self.ensure_valid_token()

        # 使用旧版API接口
        url = "https://oapi.dingtalk.com/topapi/v2/user/get"
        params = {"access_token": self._token_info.access_token}
        data = {
            "userid": user_id,
            "language": language
        }

        response = requests.post(url, params=params, json=data)
        print(response.text)
        if response.status_code != 200:
            raise Exception(f"获取用户信息失败: {response.status_code} - {response.text}")

        result = response.json()

        if result.get("errcode", 0) != 0:
            raise Exception(f"获取用户信息失败: {result.get('errmsg', '未知错误')}")

        return result.get("result", {})

    def get_union_id_by_user_id(self, user_id: str) -> str:
        """
        通过用户ID获取unionId

        Args:
            user_id: 用户ID

        Returns:
            str: unionId

        Raises:
            Exception: 获取unionId失败时抛出异常
        """
        user_info = self.get_user_info(user_id)
        union_id = user_info.get("unionid")

        if not union_id:
            raise Exception(f"无法获取用户 {user_id} 的unionId")

        return union_id

    def get_upload_info(self, space_id: str, union_id: str,
                       protocol: str = "HEADER_SIGNATURE",
                       multipart: bool = False,
                       storage_driver: Optional[str] = None) -> UploadInfo:
        """
        获取文件上传信息

        Args:
            space_id: 空间ID
            union_id: 操作者unionId
            protocol: 上传协议，默认HEADER_SIGNATURE
            multipart: 是否分片上传，5G以下设为False，5G以上必须设为True
            storage_driver: 存储驱动类型

        Returns:
            UploadInfo: 上传信息对象

        Raises:
            Exception: 请求失败时抛出异常
        """
        # 确保token有效
        self.ensure_valid_token()

        url = f"{self.base_url}/v1.0/storage/spaces/{space_id}/files/uploadInfos/query"
        params = {"unionId": union_id}

        data = {
            "protocol": protocol,
            "multipart": multipart
        }

        if storage_driver:
            data["option"] = {"storageDriver": storage_driver}

        response = self.session.post(url, params=params, json=data)
        
        if response.status_code != 200:
            raise Exception(f"获取上传信息失败: {response.status_code} - {response.text}")
        
        result = response.json()
        
        # 解析返回结果
        header_info = result.get("headerSignatureInfo", {})
        
        return UploadInfo(
            upload_key=result.get("uploadKey"),
            storage_driver=result.get("storageDriver"),
            protocol=result.get("protocol"),
            resource_urls=header_info.get("resourceUrls", []),
            headers=header_info.get("headers", {}),
            expiration_seconds=header_info.get("expirationSeconds", 0),
            region=header_info.get("region", "UNKNOWN")
        )
    
    def upload_file_to_oss(self, file_path: str, upload_info: UploadInfo) -> bool:
        """
        使用OSS的header加签方式上传文件
        
        Args:
            file_path: 本地文件路径
            upload_info: 上传信息
            
        Returns:
            bool: 上传是否成功
            
        Raises:
            Exception: 上传失败时抛出异常
        """
        if not upload_info.resource_urls:
            raise Exception("没有可用的上传URL")
        
        if not os.path.exists(file_path):
            raise Exception(f"文件不存在: {file_path}")
        
        # 使用第一个资源URL进行上传
        resource_url = upload_info.resource_urls[0]
        
        try:
            with open(file_path, 'rb') as file:
                response = requests.put(
                    resource_url,
                    data=file,
                    headers=upload_info.headers,
                    timeout=300  # 5分钟超时
                )
            
            if response.status_code in [200, 201, 204]:
                return True
            else:
                raise Exception(f"OSS上传失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            raise Exception(f"文件上传到OSS失败: {str(e)}")
    
    def commit_file(self, space_id: str, union_id: str, upload_key: str, 
                   file_name: str, parent_id: str = "0") -> FileInfo:
        """
        提交文件完成上传

        Args:
            space_id: 空间ID
            union_id: 操作者unionId
            upload_key: 上传唯一标识
            file_name: 文件名称（带后缀）
            parent_id: 父目录ID，根目录为"0"

        Returns:
            FileInfo: 文件信息对象

        Raises:
            Exception: 提交失败时抛出异常
        """
        # 确保token有效
        self.ensure_valid_token()

        url = f"{self.base_url}/v1.0/storage/spaces/{space_id}/files/commit"
        params = {"unionId": union_id}

        data = {
            "uploadKey": upload_key,
            "name": file_name,
            "parentId": parent_id
        }

        response = self.session.post(url, params=params, json=data)
        
        if response.status_code != 200:
            raise Exception(f"提交文件失败: {response.status_code} - {response.text}")
        
        result = response.json()
        dentry = result.get("dentry", {})
        
        return FileInfo(
            id=dentry.get("id"),
            space_id=dentry.get("spaceId"),
            parent_id=dentry.get("parentId"),
            type=dentry.get("type"),
            name=dentry.get("name"),
            size=dentry.get("size", 0),
            path=dentry.get("path"),
            version=dentry.get("version", 0),
            status=dentry.get("status"),
            extension=dentry.get("extension"),
            creator_id=dentry.get("creatorId"),
            modifier_id=dentry.get("modifierId"),
            create_time=dentry.get("createTime"),
            modified_time=dentry.get("modifiedTime"),
            uuid=dentry.get("uuid"),
            partition_type=dentry.get("partitionType"),
            storage_driver=dentry.get("storageDriver")
        )

    def send_file_to_conversation(self, space_id: str, dentry_id: str,
                                open_conversation_id: str, union_id: str) -> ConversationFileInfo:
        """
        发送文件到指定会话

        Args:
            space_id: 空间ID
            dentry_id: 文件ID
            open_conversation_id: 目标会话的openConversationId
            union_id: 操作者unionId

        Returns:
            ConversationFileInfo: 发送到会话的文件信息

        Raises:
            Exception: 发送失败时抛出异常
        """
        # 确保token有效
        self.ensure_valid_token()

        url = f"{self.base_url}/v1.0/convFile/conversations/files/send"
        params = {"unionId": union_id}

        data = {
            "spaceId": space_id,
            "dentryId": dentry_id,
            "openConversationId": open_conversation_id
        }

        response = self.session.post(url, params=params, json=data)

        if response.status_code != 200:
            raise Exception(f"发送文件到会话失败: {response.status_code} - {response.text}")

        result = response.json()
        file_data = result.get("file", {})

        return ConversationFileInfo(
            id=file_data.get("id"),
            conversation_id=file_data.get("conversationId"),
            space_id=file_data.get("spaceId"),
            parent_id=file_data.get("parentId"),
            type=file_data.get("type"),
            name=file_data.get("name"),
            size=file_data.get("size", 0),
            path=file_data.get("path"),
            version=file_data.get("version", 0),
            status=file_data.get("status"),
            extension=file_data.get("extension"),
            creator_id=file_data.get("creatorId"),
            modifier_id=file_data.get("modifierId"),
            create_time=file_data.get("createTime"),
            modified_time=file_data.get("modifiedTime"),
            uuid=file_data.get("uuid")
        )

    def send_existing_file_to_conversation(self, space_id: str, file_id: str,
                                         open_conversation_id: str, union_id: str) -> ConversationFileInfo:
        """
        发送已存在的文件到指定会话

        Args:
            space_id: 空间ID
            file_id: 已存在的文件ID
            open_conversation_id: 目标会话的openConversationId
            union_id: 操作者unionId

        Returns:
            ConversationFileInfo: 发送到会话的文件信息

        Raises:
            Exception: 发送失败时抛出异常
        """
        print(f"发送已存在文件到会话: 文件ID={file_id}, 会话ID={open_conversation_id}")

        return self.send_file_to_conversation(
            space_id=space_id,
            dentry_id=file_id,
            open_conversation_id=open_conversation_id,
            union_id=union_id
        )

    def upload_file_complete(self, space_id: str, union_id: str, file_path: str,
                           file_name: Optional[str] = None, parent_id: Optional[str] = None,
                           multipart: Optional[bool] = None) -> FileInfo:
        """
        完整的文件上传流程

        Args:
            space_id: 空间ID
            union_id: 操作者unionId
            file_path: 本地文件路径
            file_name: 文件名称，如果不提供则使用文件路径中的文件名
            parent_id: 父目录ID，如果不提供则使用配置中的默认文件夹ID
            multipart: 是否分片上传，如果不提供则根据文件大小自动判断

        Returns:
            FileInfo: 上传后的文件信息

        Raises:
            Exception: 上传过程中任何步骤失败时抛出异常
        """
        if not os.path.exists(file_path):
            raise Exception(f"文件不存在: {file_path}")

        # 如果没有提供文件名，则从路径中提取
        if file_name is None:
            file_name = os.path.basename(file_path)

        # 如果没有提供父目录ID，使用默认的文件夹ID
        if parent_id is None:
            try:
                from .config import DingTalkConfig
            except ImportError:
                from config import DingTalkConfig
            config = DingTalkConfig()
            parent_id = config.default_parent_id
            print(f"使用默认文件夹ID: {parent_id}")

        # 如果没有指定是否分片，根据文件大小自动判断（5GB = 5 * 1024 * 1024 * 1024）
        if multipart is None:
            file_size = os.path.getsize(file_path)
            multipart = file_size > 5 * 1024 * 1024 * 1024
        
        print(f"开始上传文件: {file_name}")
        print(f"文件大小: {os.path.getsize(file_path)} bytes")
        print(f"分片上传: {multipart}")
        
        # 步骤1: 获取上传信息
        print("步骤1: 获取上传信息...")
        upload_info = self.get_upload_info(space_id, union_id, multipart=multipart)
        print(f"获取到上传信息，uploadKey: {upload_info.upload_key}")
        
        # 步骤2: 上传文件到OSS
        print("步骤2: 上传文件到OSS...")
        success = self.upload_file_to_oss(file_path, upload_info)
        if not success:
            raise Exception("文件上传到OSS失败")
        print("文件上传到OSS成功")
        
        # 步骤3: 提交文件
        print("步骤3: 提交文件...")
        file_info = self.commit_file(space_id, union_id, upload_info.upload_key, 
                                   file_name, parent_id)
        print(f"文件上传完成，文件ID: {file_info.id}")
        
        return file_info

    def upload_and_send_to_conversation(self, space_id: str, union_id: str, file_path: str,
                                      open_conversation_id: str, file_name: Optional[str] = None,
                                      parent_id: Optional[str] = None, multipart: Optional[bool] = None) -> ConversationFileInfo:
        """
        上传文件并发送到指定会话的完整流程

        Args:
            space_id: 空间ID
            union_id: 操作者unionId
            file_path: 本地文件路径
            open_conversation_id: 目标会话的openConversationId
            file_name: 文件名称，如果不提供则使用文件路径中的文件名
            parent_id: 父目录ID，如果不提供则使用配置中的默认文件夹ID
            multipart: 是否分片上传，如果不提供则根据文件大小自动判断

        Returns:
            ConversationFileInfo: 发送到会话的文件信息

        Raises:
            Exception: 上传或发送过程中任何步骤失败时抛出异常
        """
        print(f"开始上传文件并发送到会话: {file_path}")

        # 步骤1: 上传文件
        file_info = self.upload_file_complete(
            space_id=space_id,
            union_id=union_id,
            file_path=file_path,
            file_name=file_name,
            parent_id=parent_id,
            multipart=multipart
        )

        print(f"文件上传完成，文件ID: {file_info.id}")

        # 步骤2: 发送文件到会话
        conversation_file = self.send_file_to_conversation(
            space_id=space_id,
            dentry_id=file_info.id,
            open_conversation_id=open_conversation_id,
            union_id=union_id
        )

        print(f"文件已发送到会话，会话ID: {conversation_file.conversation_id}")

        return conversation_file

    def upload_file_to_group_conversation(self, file_path: str, file_name: Optional[str] = None,
                                        open_conversation_id: Optional[str] = None,
                                        space_id: Optional[str] = None, union_id: Optional[str] = None,
                                        user_id: Optional[str] = None) -> ConversationFileInfo:
        """
        上传本地文件到群会话的便捷函数

        Args:
            file_path: 本地文件路径
            file_name: 文件名称，如果不提供则使用文件路径中的文件名
            open_conversation_id: 目标会话ID，如果不提供则使用配置中的默认值
            space_id: 空间ID，如果不提供则使用配置中的默认值
            union_id: 操作者unionId，如果不提供则通过user_id自动获取
            user_id: 用户ID，如果不提供则使用配置中的默认值

        Returns:
            ConversationFileInfo: 发送到会话的文件信息

        Raises:
            Exception: 上传或发送过程中任何步骤失败时抛出异常
        """
        try:
            from .config import DingTalkConfig
        except ImportError:
            from config import DingTalkConfig
        config = DingTalkConfig()

        # 使用提供的参数或配置中的默认值
        if space_id is None:
            space_id = config.space_id
        if open_conversation_id is None:
            open_conversation_id = config.open_conversation_id
        if user_id is None:
            user_id = config.user_id

        # 获取unionId
        if union_id is None:
            union_id = self.get_union_id_by_user_id(user_id)

        # 如果没有提供文件名，则从路径中提取
        if file_name is None:
            file_name = os.path.basename(file_path)

        print(f"📤 上传文件到群会话:")
        print(f"   - 文件路径: {file_path}")
        print(f"   - 文件名: {file_name}")
        print(f"   - 会话ID: {open_conversation_id}")
        print(f"   - 空间ID: {space_id}")
        print(f"   - 用户ID: {user_id}")

        # 调用完整的上传并发送流程
        return self.upload_and_send_to_conversation(
            space_id=space_id,
            union_id=union_id,
            file_path=file_path,
            open_conversation_id=open_conversation_id,
            file_name=file_name
        )


# 为了向后兼容，保留原来的类名
DingTalkFileUploadSDK = DingTalkSDK


def main():
    """示例用法"""
    # 配置信息
    APP_KEY = "your_app_key_here"
    APP_SECRET = "your_app_secret_here"
    SPACE_ID = "your_space_id_here"
    UNION_ID = "your_union_id_here"
    OPEN_CONVERSATION_ID = "your_open_conversation_id_here"

    # 方式1: 使用app_key和app_secret自动获取token
    sdk = DingTalkSDK(app_key=APP_KEY, app_secret=APP_SECRET)

    # 方式2: 直接使用已有的access_token
    # sdk = DingTalkSDK(access_token="your_access_token_here")

    try:
        print("=== 钉钉SDK功能演示 ===")

        # 获取访问令牌
        print("1. 获取访问令牌...")
        access_token = sdk.get_access_token()
        print(f"✅ 获取成功: {access_token[:20]}...")

        # 上传文件
        print("\n2. 上传文件...")
        file_info = sdk.upload_file_complete(
            space_id=SPACE_ID,
            union_id=UNION_ID,
            file_path="./test_file.txt",  # 替换为实际文件路径
            file_name="钉钉SDK测试文件.txt",
            parent_id="0"  # 根目录
        )

        print("✅ 文件上传成功!")
        print(f"📄 文件ID: {file_info.id}")
        print(f"📝 文件名: {file_info.name}")
        print(f"📏 文件大小: {file_info.size} bytes")
        print(f"📂 文件路径: {file_info.path}")

        # 发送文件到会话
        print("\n3. 发送文件到会话...")
        conversation_file = sdk.send_file_to_conversation(
            space_id=SPACE_ID,
            dentry_id=file_info.id,
            open_conversation_id=OPEN_CONVERSATION_ID,
            union_id=UNION_ID
        )

        print("✅ 文件发送成功!")
        print(f"💬 会话ID: {conversation_file.conversation_id}")
        print(f"📄 文件名: {conversation_file.name}")

        # 一步完成上传并发送
        print("\n4. 一步完成上传并发送...")
        result = sdk.upload_and_send_to_conversation(
            space_id=SPACE_ID,
            union_id=UNION_ID,
            file_path="./test_file2.txt",  # 替换为实际文件路径
            open_conversation_id=OPEN_CONVERSATION_ID,
            file_name="一步上传发送测试.txt"
        )

        print("✅ 一步上传发送成功!")
        print(f"💬 会话ID: {result.conversation_id}")

    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")


if __name__ == "__main__":
    main()
