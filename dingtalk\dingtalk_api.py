import requests
import time
import hashlib
import hmac
import base64
import urllib.parse
import json
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class DingTalkAPI:
    """
    钉钉API封装类 - 包含核心功能和机器人消息发送
    """
    def __init__(self, app_key: str = None, app_secret: str = None, webhook_url: str = None, webhook_secret: str = None, log_level: str = "INFO"):
        self.app_key = app_key
        self.app_secret = app_secret
        self.webhook_url = webhook_url
        self.webhook_secret = webhook_secret
        self.access_token = None
        self.token_expire_time = 0
        self.base_url = "https://oapi.dingtalk.com"
        self.log_level = log_level.upper()
        self.logger = logging.getLogger("DingTalkAPI")
        if self.log_level == "DEBUG":
            self.logger.setLevel(logging.DEBUG)
        else:
            self.logger.setLevel(logging.INFO)

    def get_access_token(self) -> Optional[str]:
        try:
            current_time = int(time.time())
            if self.access_token and current_time < self.token_expire_time:
                return self.access_token
            if not self.app_key or not self.app_secret:
                self.logger.error("AppKey或AppSecret未配置")
                return None
            url = f"{self.base_url}/gettoken"
            params = {
                "appkey": self.app_key,
                "appsecret": self.app_secret
            }
            if self.log_level == "DEBUG":
                self.logger.debug(f"[DingTalkAPI] 请求接口: {url}")
                self.logger.debug(f"[DingTalkAPI] 请求参数: {params}")
            response = requests.get(url, params=params, timeout=10)
            result = response.json()
            if self.log_level == "DEBUG":
                self.logger.debug(f"[DingTalkAPI] 返回结果: {result}")
            if result.get("errcode") == 0:
                self.access_token = result.get("access_token")
                expires_in = result.get("expires_in", 7200)
                self.token_expire_time = current_time + expires_in - 300
                self.logger.info("获取access_token成功")
                return self.access_token
            else:
                self.logger.error(f"获取access_token失败: {result}")
                return None
        except Exception as e:
            self.logger.error(f"获取access_token异常: {str(e)}")
            return None

    def _generate_webhook_signature(self, timestamp: str, secret: str) -> str:
        """生成webhook签名"""
        string_to_sign = f"{timestamp}\n{secret}"
        hmac_code = hmac.new(
            secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign

    def send_robot_message(self, content: str, at_user_id: Optional[str] = None,
                          at_mobiles: Optional[List[str]] = None, is_at_all: bool = False) -> bool:
        """
        发送机器人消息到钉钉群

        Args:
            content: 消息内容
            at_user_id: 要@的用户ID
            at_mobiles: 要@的手机号列表
            is_at_all: 是否@所有人

        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            self.logger.error("webhook_url未配置，无法发送机器人消息")
            return False

        try:
            # 构建消息体
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "text": content
                },
                "at": {
                    "isAtAll": is_at_all
                }
            }

            # 处理@用户
            if at_user_id:
                data["at"]["atUserIds"] = [at_user_id]

            if at_mobiles:
                data["at"]["atMobiles"] = at_mobiles

            # 如果配置了webhook密钥，添加签名
            url = self.webhook_url
            if self.webhook_secret:
                timestamp = str(round(time.time() * 1000))
                sign = self._generate_webhook_signature(timestamp, self.webhook_secret)
                url = f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"

            if self.log_level == "DEBUG":
                self.logger.debug(f"[DingTalkAPI] 发送机器人消息: {url}")
                self.logger.debug(f"[DingTalkAPI] 消息内容: {data}")

            # 发送请求
            response = requests.post(url, json=data, timeout=10)
            result = response.json()

            if self.log_level == "DEBUG":
                self.logger.debug(f"[DingTalkAPI] 机器人消息响应: {result}")

            if result.get("errcode") == 0:
                self.logger.info("机器人消息发送成功")
                return True
            else:
                self.logger.error(f"机器人消息发送失败: {result}")
                return False

        except Exception as e:
            self.logger.error(f"发送机器人消息异常: {str(e)}")
            return False

    def search_user_by_name(self, name: str) -> Optional[Dict]:
        """
        根据姓名搜索用户信息

        Args:
            name: 用户姓名

        Returns:
            Dict: 用户信息，包含userid等字段，如果未找到返回None
        """
        try:
            access_token = self.get_access_token()
            if not access_token:
                self.logger.error("无法获取access_token，搜索用户失败")
                return None

            # 使用钉钉API搜索用户
            url = f"{self.base_url}/topapi/v2/user/search"
            params = {"access_token": access_token}
            data = {
                "query_word": name,
                "max_results": 10
            }

            if self.log_level == "DEBUG":
                self.logger.debug(f"[DingTalkAPI] 搜索用户: {url}")
                self.logger.debug(f"[DingTalkAPI] 搜索参数: {data}")

            response = requests.post(url, params=params, json=data, timeout=10)
            result = response.json()

            if self.log_level == "DEBUG":
                self.logger.debug(f"[DingTalkAPI] 搜索用户响应: {result}")

            if result.get("errcode") == 0:
                user_list = result.get("result", {}).get("list", [])
                # 查找完全匹配的用户
                for user in user_list:
                    if user.get("name") == name:
                        self.logger.info(f"找到用户: {name} -> {user.get('userid')}")
                        return user

                self.logger.warning(f"未找到完全匹配的用户: {name}")
                return None
            else:
                self.logger.error(f"搜索用户失败: {result}")
                return None

        except Exception as e:
            self.logger.error(f"搜索用户异常: {str(e)}")
            return None