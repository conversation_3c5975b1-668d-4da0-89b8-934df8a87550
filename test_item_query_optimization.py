#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试商品查询优化效果
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_format_simple_item_info():
    """测试简化商品信息格式化"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session"
        )
        
        # 模拟商品数据
        item_data = {
            'sysItemId': '622517776007680',
            'outerId': '742261468349',
            'title': '断桥铝合金门智能锁推拉玻璃门密码锁窄边庭院门户外防水指纹门锁',
            'mapping_info': {
                'platform_id': '786591249844',
                'outer_id': '742261468349',
                'user_id': 123456,
                'taobao_id': 789012
            }
        }
        
        print("🔍 测试简化商品信息格式化:")
        print("=" * 50)
        
        # 测试简化格式
        simple_info = api.format_simple_item_info(item_data)
        print("简化格式输出:")
        print(simple_info)
        print()
        
        # 测试详细格式（简化版）
        detailed_info = api.format_item_info(item_data, show_detailed=False)
        print("详细格式（简化版）输出:")
        print(detailed_info)
        print()
        
        # 检查是否包含必要字段
        required_fields = [
            "系统商品ID",
            "平台商品ID", 
            "平台商家编码",
            "商品标题"
        ]
        
        for field in required_fields:
            if field in simple_info:
                print(f"✅ 包含字段: {field}")
            else:
                print(f"❌ 缺少字段: {field}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_item_data_without_mapping():
    """测试没有映射信息的商品数据"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session"
        )
        
        # 模拟没有映射信息的商品数据
        item_data = {
            'sysItemId': '622517776007680',
            'outerId': '742261468349',
            'title': '断桥铝合金门智能锁推拉玻璃门密码锁窄边庭院门户外防水指纹门锁'
        }
        
        print("\n🔍 测试没有映射信息的商品数据:")
        print("=" * 50)
        
        simple_info = api.format_simple_item_info(item_data)
        print("简化格式输出:")
        print(simple_info)
        
        # 检查是否正确处理缺失的平台商品ID
        if "平台商品ID" not in simple_info:
            print("✅ 正确处理缺失的平台商品ID（不显示该字段）")
        else:
            print("⚠️ 显示了平台商品ID字段，但数据中没有映射信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_method_existence():
    """测试新方法是否存在"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session"
        )
        
        print("\n🔍 测试方法存在性:")
        print("=" * 50)
        
        methods_to_check = [
            'format_simple_item_info',
            'format_item_info'
        ]
        
        for method_name in methods_to_check:
            if hasattr(api, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试商品查询优化...")
    print("=" * 60)
    
    tests = [
        ("方法存在性", test_method_existence),
        ("简化商品信息格式化", test_format_simple_item_info),
        ("无映射信息处理", test_item_data_without_mapping)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！商品查询优化成功！")
        print("\n📋 优化内容:")
        print("• 简化了商品信息显示，只显示核心字段")
        print("• 添加了平台商品ID显示")
        print("• 修复了字段映射问题")
        print("• 优化了批量查询的显示格式")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
