#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试商品查询日志输出
"""

import sys
import os
import logging

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置日志级别为DEBUG以查看所有日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_single_item_query():
    """测试单个商品查询的日志输出"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        # 创建API实例，设置为DEBUG级别
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="DEBUG"
        )
        
        print("🔍 测试单个商品查询日志输出:")
        print("=" * 60)
        
        # 模拟查询（这会失败，但我们可以看到日志）
        print("\n📋 测试系统商家编码查询:")
        result = api.query_single_item(outer_id="742261468349")
        print(f"查询结果: {result}")
        
        print("\n📋 测试系统商品ID查询:")
        result = api.query_single_item(sys_item_id=622517776007680)
        print(f"查询结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_mapping_query():
    """测试商品对应关系查询的日志输出"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        # 创建API实例，设置为DEBUG级别
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="DEBUG"
        )
        
        print("\n🔍 测试商品对应关系查询日志输出:")
        print("=" * 60)
        
        # 测试通过系统商家编码查询对应关系
        print("\n📋 测试系统商家编码对应关系查询:")
        result = api.query_item_outer_id_mapping(outer_ids=["742261468349"])
        print(f"查询结果: {result}")
        
        # 测试通过平台商品ID查询对应关系
        print("\n📋 测试平台商品ID对应关系查询:")
        result = api.query_item_outer_id_mapping(
            num_iid_list=["786591249844"], 
            taobao_id=123456
        )
        print(f"查询结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_format_functions():
    """测试格式化函数的日志输出"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        api = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="DEBUG"
        )
        
        print("\n🔍 测试格式化函数:")
        print("=" * 60)
        
        # 模拟商品数据（包含平台信息）
        item_data_with_platform = {
            'sysItemId': '622517776007680',
            'outerId': '742261468349',
            'title': '断桥铝合金门智能锁推拉玻璃门密码锁窄边庭院门户外防水指纹门锁',
            'platform_info': {
                'platform_item_id': '786591249844',
                'platform_outer_id': 'PLATFORM_CODE_123',  # 真正的平台商家编码
                'platform_sku_id': 'SKU123456',
                'platform_sku_outer_id': 'SKU_CODE_123',
                'platform_url': 'https://item.taobao.com/item.htm?id=786591249844',
                'taobao_id': 123456,
                'user_id': 789012
            }
        }
        
        print("\n📋 测试包含平台信息的商品格式化:")
        formatted = api.format_simple_item_info(item_data_with_platform)
        print("格式化结果:")
        print(formatted)
        
        # 模拟商品数据（不包含平台信息）
        item_data_without_platform = {
            'sysItemId': '622517776007680',
            'outerId': '742261468349',
            'title': '断桥铝合金门智能锁推拉玻璃门密码锁窄边庭院门户外防水指纹门锁'
        }
        
        print("\n📋 测试不包含平台信息的商品格式化:")
        formatted = api.format_simple_item_info(item_data_without_platform)
        print("格式化结果:")
        print(formatted)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_log_levels():
    """测试不同日志级别的输出"""
    try:
        from kuaimai.kuaimai_api import KuaimaiAPI
        
        print("\n🔍 测试不同日志级别:")
        print("=" * 60)
        
        # 测试INFO级别
        print("\n📋 INFO级别日志:")
        api_info = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="INFO"
        )
        api_info.query_single_item(outer_id="742261468349")
        
        # 测试DEBUG级别
        print("\n📋 DEBUG级别日志:")
        api_debug = KuaimaiAPI(
            app_key="test_key",
            secret="test_secret",
            session="test_session",
            log_level="DEBUG"
        )
        api_debug.query_single_item(outer_id="742261468349")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试商品查询日志输出...")
    print("=" * 80)
    
    tests = [
        ("单个商品查询日志", test_single_item_query),
        ("商品对应关系查询日志", test_mapping_query),
        ("格式化函数测试", test_format_functions),
        ("日志级别测试", test_log_levels)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！日志功能正常！")
        print("\n📋 日志功能说明:")
        print("• 添加了详细的商品查询日志")
        print("• 记录了商家编码和API返回结果")
        print("• 支持DEBUG和INFO两种日志级别")
        print("• 包含商品对应关系查询的详细日志")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
